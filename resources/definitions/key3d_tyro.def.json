{"version": 2, "name": "<PERSON><PERSON>", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "DragonJe", "manufacturer": "Key3D", "file_formats": "text/x-gcode", "has_machine_quality": true, "has_materials": true, "has_variants": false, "machine_extruder_trains": {"0": "key3d_tyro_extruder_0"}, "platform_offset": [0, 0, 0], "preferred_material": "generic_pla", "preferred_quality_type": "normal"}, "overrides": {"gantry_height": {"value": "30"}, "machine_depth": {"default_value": 150}, "machine_end_gcode": {"default_value": "M104 S0\nM140 S0\n ; Retract the filament\nG92 E1\nG1 E-1 F300\nG28 X0 Y0\nM84"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-30, 34], [-30, -32], [30, -32], [30, 34]]}, "machine_heated_bed": {"default_value": false}, "machine_heated_build_volume": {"default_value": false}, "machine_height": {"default_value": 150}, "machine_name": {"default_value": "<PERSON><PERSON>"}, "machine_start_gcode": {"default_value": "G28 ; Home\nG1 Z15.0 F6000 ; Move Z axis up 15mm\n ; Prime the extruder\nG92 E0\nG1 F200 E3\nG92 E0"}, "machine_width": {"default_value": 150}, "material_diameter": {"default_value": 1.75}}}