{"version": 2, "name": "Snapmaker 2", "inherits": "fdmprinter", "metadata": {"visible": false, "manufacturer": "Snapmaker", "file_formats": "text/x-gcode", "exclude_materials": [], "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "snapmaker_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal"}, "overrides": {"default_material_print_temperature": {"default_value": 205}, "machine_acceleration": {"default_value": 1000}, "machine_buildplate_type": {"default_value": "aluminum"}, "machine_end_gcode": {"default_value": "M104 S0 ;Extruder heater off\nM140 S0 ;Heated bed heater off\nG90 ;absolute positioning\nG92 E0 ;Retract the filament\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z{machine_height} E-1 F3000 ;move Z up a bit and retract filament even more\nG1 X0 F3000 ;move X to min endstops, so the head is out of the way\nG1 Y{machine_depth} F3000 ;so the head is out of the way and Plate is moved forward"}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"default_value": 1000}, "machine_max_acceleration_x": {"default_value": 1000}, "machine_max_acceleration_y": {"default_value": 1000}, "machine_max_acceleration_z": {"default_value": 1000}, "machine_name": {"default_value": "Snapmaker"}, "machine_nozzle_size": {"default_value": 0.4}, "machine_start_gcode": {"default_value": "M104 S{material_print_temperature_layer_0} ;Set Hotend Temperature\nM140 S{material_bed_temperature_layer_0} ;Set Bed Temperature\nG28 ;home\nG90 ;absolute positioning\nG1 X-10 Y-10 F3000 ;Move to corner \nG1 Z0 F1800 ;Go to zero offset\nM109 S{material_print_temperature_layer_0} ;Wait for Hotend Temperature\nM190 S{material_bed_temperature_layer_0} ;Wait for Bed Temperature\nG92 E0 ;Zero set extruder position\nG1 E20 F200 ;Feed filament to clear nozzle\nG92 E0 ;Zero set extruder position"}, "material_bed_temp_prepend": {"default_value": false}, "material_diameter": {"default_value": 1.75}, "material_print_temp_prepend": {"default_value": false}, "retract_at_layer_change": {"default_value": false}, "retraction_amount": {"default_value": 5}, "retraction_enable": {"default_value": true}, "retraction_speed": {"default_value": 60}}}