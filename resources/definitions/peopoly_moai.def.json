{"version": 2, "name": "Peopoly Moai", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Peopoly", "manufacturer": "Peopoly", "file_formats": "text/x-gcode", "platform": "moai.obj", "has_machine_quality": true, "has_materials": false, "has_textured_buildplate": true, "machine_extruder_trains": {"0": "peopoly_moai_extruder_0"}, "platform_texture": "moai.jpg"}, "overrides": {"acceleration_enabled": {"enabled": false, "value": "False"}, "adaptive_layer_height_enabled": {"enabled": false, "value": "False"}, "adhesion_type": {"value": "'none'"}, "bottom_thickness": {"minimum_value_warning": "resolveOrValue('layer_height')"}, "bridge_settings_enabled": {"enabled": false, "value": "False"}, "coasting_enable": {"enabled": false, "value": "False"}, "cool_fan_enabled": {"enabled": false, "value": "False"}, "cool_fan_full_at_height": {"enabled": false}, "cool_fan_full_layer": {"enabled": false}, "cool_fan_speed_min": {"enabled": false, "value": 0}, "cool_lift_head": {"enabled": false, "value": "False"}, "cool_min_layer_time": {"enabled": false, "value": 0}, "cool_min_layer_time_fan_speed_max": {"enabled": false}, "cool_min_speed": {"enabled": false, "value": 0}, "draft_shield_enabled": {"enabled": false, "value": "False"}, "expand_skins_expand_distance": {"value": "( wall_line_width_0 + (wall_line_count - 1) * wall_line_width_x ) / 2"}, "flow_rate_extrusion_offset_factor": {"enabled": false}, "flow_rate_max_extrusion_offset": {"enabled": false}, "infill_angles": {"value": "[0, 90]"}, "infill_line_width": {"minimum_value_warning": "machine_nozzle_size"}, "infill_overlap": {"value": 15}, "infill_pattern": {"value": "'lines'"}, "infill_sparse_density": {"value": 70}, "infill_sparse_thickness": {"maximum_value_warning": "0.5"}, "infill_wipe_dist": {"value": 0}, "ironing_enabled": {"enabled": false, "value": "False"}, "layer_height": {"maximum_value_warning": "0.5", "minimum_value_warning": "0.02"}, "layer_height_0": {"maximum_value_warning": "0.5", "minimum_value_warning": "0.02", "value": "0.1"}, "line_width": {"minimum_value_warning": "machine_nozzle_size"}, "machine_depth": {"default_value": 130}, "machine_end_gcode": {"default_value": "M104 S0\nM140 S0\nG28 X0 Y0\nM84"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-20, 10], [-20, -10], [10, 10], [10, -10]]}, "machine_height": {"default_value": 180}, "machine_name": {"default_value": "<PERSON><PERSON>"}, "machine_nozzle_temp_enabled": {"value": "False"}, "machine_start_gcode": {"default_value": "G28 ;Home"}, "machine_width": {"default_value": 130}, "material_bed_temperature": {"enabled": false}, "material_flow": {"enabled": false}, "material_flow_layer_0": {"enabled": false}, "meshfix_maximum_deviation": {"value": "0.002"}, "minimum_polygon_circumference": {"value": "0.1"}, "print_sequence": {"enabled": false}, "relative_extrusion": {"enabled": false, "value": "False"}, "retract_at_layer_change": {"enabled": false, "value": false}, "retraction_combing": {"enabled": false, "value": "'off'"}, "retraction_enable": {"enabled": false, "value": "False"}, "skin_line_width": {"minimum_value_warning": "machine_nozzle_size"}, "skin_no_small_gaps_heuristic": {"value": "False"}, "skin_outline_count": {"value": 0}, "skin_overlap": {"value": 5}, "skirt_brim_line_width": {"minimum_value_warning": "machine_nozzle_size"}, "speed_infill": {"maximum_value_warning": "300"}, "speed_layer_0": {"value": 5}, "speed_print": {"maximum_value_warning": "300"}, "speed_slowdown_layers": {"value": 3}, "speed_topbottom": {"maximum_value_warning": "300", "value": "speed_print"}, "speed_travel": {"value": 150}, "speed_travel_layer_0": {"value": 150}, "speed_wall": {"maximum_value_warning": "300", "value": "speed_print"}, "speed_wall_0": {"maximum_value_warning": "300"}, "speed_wall_x": {"maximum_value_warning": "300", "value": "speed_print"}, "support_enable": {"enabled": false}, "top_bottom_thickness": {"minimum_value_warning": "0.1", "value": "0.1"}, "top_thickness": {"minimum_value_warning": "resolveOrValue('layer_height')"}, "wall_0_wipe_dist": {"value": "machine_nozzle_size / 3"}, "wall_line_width": {"minimum_value_warning": "machine_nozzle_size"}, "wall_line_width_x": {"minimum_value_warning": "machine_nozzle_size"}, "wall_thickness": {"value": "0.5"}, "z_seam_corner": {"value": "'z_seam_corner_none'"}, "z_seam_type": {"value": "'shortest'"}}}