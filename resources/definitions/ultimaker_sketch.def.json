{"version": 2, "name": "MakerBot Sketch", "inherits": "ultimaker", "metadata": {"visible": true, "author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Ultimaker B.V.", "file_formats": "application/x-makerbot-sketch", "platform": "ultimaker_sketch_platform.obj", "exclude_materials": ["dsm_", "Essentium_", "imade3d_", "chromatik_", "3D-Fuel_", "bestfilament_", "eazao_", "emotiontech_", "eryone_", "eSUN_", "Extrudr_", "fabtotum_", "fdplast_", "filo3d_", "ultimaker_rapidrinse_175", "goofoo_", "ideagen3D_", "imade3d_", "innofill_", "layer_one_", "leapfrog_", "polyflex_pla", "polymax_pla", "polyplus_pla", "polywood_pla", "redd_", "tizyx_", "verbatim_", "Vertex_", "volumic_", "xyzprinting_", "zyyx_pro_", "octofiber_", "fiberlogy_", "generic_", "ultimaker_asa", "ultimaker_abs", "ultimaker_nylon", "ultimaker_pva", "ultimaker_rapidrinse", "ultimaker_sr30", "ultimaker_petg", "basf_", "jabil_", "polymaker_", "ultimaker_pc-abs", "ultimaker_pc-abs-fr"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "ultimaker_sketch_extruder"}, "preferred_material": "ultimaker_pla_175", "preferred_quality_type": "draft", "preferred_variant_name": "0.4mm", "reference_machine_id": "sketch", "supports_network_connection": true, "supports_usb_connection": false, "variants_name": "Extruder", "variants_name_has_translation": true, "weight": -1}, "overrides": {"acceleration_enabled": {"enabled": false, "value": false}, "adhesion_type": {"value": "'skirt'"}, "brim_width": {"value": "3"}, "cool_during_extruder_switch": {"enabled": false, "value": false}, "cool_fan_full_at_height": {"value": "layer_height + layer_height_0"}, "cool_fan_speed": {"value": 100}, "cool_fan_speed_0": {"value": 0}, "cool_min_layer_time": {"value": 8}, "extruder_prime_pos_abs": {"default_value": true}, "fill_outline_gaps": {"value": false}, "gantry_height": {"value": "60"}, "infill_angles": {"value": "[45,45,45,45,45,135,135,135,135,135]"}, "infill_before_walls": {"value": false}, "infill_overlap": {"value": 0}, "infill_pattern": {"value": "'zigzag'"}, "infill_sparse_density": {"value": 20}, "infill_wipe_dist": {"value": 0}, "initial_layer_line_width_factor": {"value": 125}, "inset_direction": {"value": "'inside_out'"}, "jerk_enabled": {"enabled": false, "value": false}, "layer_height_0": {"value": "layer_height * 1.25"}, "layer_start_x": {"value": "sum(extruderValues('machine_extruder_start_pos_x')) / len(extruderValues('machine_extruder_start_pos_x'))"}, "layer_start_y": {"value": "sum(extruderValues('machine_extruder_start_pos_y')) / len(extruderValues('machine_extruder_start_pos_y'))"}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 150}, "machine_end_gcode": {"default_value": "M107; Disable Fan; \n; End of print; \n; End GCode\nM104 S0 T0; Set Toolhead Temp to 0\nM140 S0 T0; Set Platform Temp to 0\nG162 Z F1800; Move to max axes position\nG28 X Y; Home\nM652; Turn off back fan\nM132 X Y Z A B; Set Home Position\nG91; Use Relative Positioning\nM18; Disable Axes\n\n"}, "machine_extruder_count": {"default_value": 1}, "machine_gcode_flavor": {"default_value": "<PERSON>"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 150}, "machine_max_feedrate_x": {"default_value": 300}, "machine_max_feedrate_y": {"default_value": 300}, "machine_max_feedrate_z": {"default_value": 40}, "machine_min_cool_heat_time_window": {"value": "15"}, "machine_name": {"default_value": "MakerBot Sketch"}, "machine_nozzle_cool_down_speed": {"default_value": 0.8}, "machine_nozzle_heat_up_speed": {"default_value": 1.4}, "machine_start_gcode": {"default_value": "M140 S50 T0; Set Platform Temp\nM104 S220 T0; Set Extruder Temp\nG90; Use Absolute Positioning\nG28; Home\nM132 X Y Z A B; Set Current Position to Home\nG161 X Y F3300; Move to min axes positions\nM7 T0; Wait For Platform to Heat\nM6 T0; Wait For Extruders to Heat\nM651; Turn on back fan\nM907 X100 Y100 Z40 A80 B20; Set Stepper Currents\nM106; Enable Cooling Fan\n; Purge Line\nG92 E0; Reset Extruder Axis Position\nG1 X-26.18 Y-75.90 Z0.200 F420\nG1 X26.18 Y-75.90 E10\nG92 E0; Reset Extruder Axis Position\n; Start GCode\n"}, "machine_width": {"default_value": 150}, "material_bed_temperature": {"maximum_value": 100, "maximum_value_warning": 70}, "material_bed_temperature_layer_0": {"maximum_value": 100, "maximum_value_warning": 70}, "material_diameter": {"default_value": 1.75}, "material_flow": {"default_value": 100}, "material_print_temperature": {"maximum_value": 240, "maximum_value_warning": 230}, "min_bead_width": {"minimum_value": "line_width * 0.5", "minimum_value_warning": "line_width * 0.75", "value": "line_width"}, "min_wall_line_width": {"minimum_value": "line_width * 0.5", "minimum_value_warning": "line_width * 0.75", "value": "line_width"}, "multiple_mesh_overlap": {"value": "0"}, "optimize_wall_printing_order": {"value": "True"}, "prime_blob_enable": {"default_value": true, "enabled": true, "value": "resolveOrValue('print_sequence') != 'one_at_a_time'"}, "print_sequence": {"enabled": false}, "raft_margin": {"value": "5"}, "retract_at_layer_change": {"value": true}, "retraction_amount": {"maximum_value": 6, "maximum_value_warning": 5.75, "value": 5.5}, "retraction_combing": {"value": "'no_outer_surfaces'"}, "retraction_min_travel": {"value": "2 * line_width"}, "retraction_prime_speed": {"value": "15"}, "retraction_speed": {"value": "25"}, "roofing_material_flow": {"value": 100}, "skin_material_flow": {"value": 95}, "skin_material_flow_layer_0": {"value": 100}, "skirt_brim_line_width": {"value": 1}, "skirt_brim_speed": {"value": 15}, "skirt_height": {"value": 3}, "speed_print": {"value": 50}, "speed_roofing": {"value": "0.8 * speed_print"}, "speed_support": {"value": "0.7 * speed_print"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "speed_roofing"}, "speed_travel": {"value": 80}, "speed_wall": {"value": "0.5 * speed_print"}, "speed_wall_0": {"value": "1 * speed_wall"}, "speed_wall_x": {"value": "1 * speed_wall"}, "speed_z_hop": {"value": 10}, "support_angle": {"value": "45"}, "support_structure": {"value": "'tree'"}, "top_bottom_thickness": {"value": "4 * layer_height"}, "travel_avoid_distance": {"value": "machine_nozzle_tip_outer_diameter / 2 * 1.5"}, "travel_avoid_supports": {"value": true}, "wall_0_inset": {"value": "0"}, "wall_0_material_flow_layer_0": {"value": "1 * material_flow"}, "wall_thickness": {"value": "2 * machine_nozzle_size"}, "wall_x_material_flow": {"value": "0.95 * material_flow"}, "wall_x_material_flow_layer_0": {"value": "1 * material_flow"}, "xy_offset": {"value": 0}, "xy_offset_layer_0": {"value": 0}, "z_seam_corner": {"value": "'z_seam_corner_any'"}, "zig_zaggify_infill": {"value": "gradual_infill_steps == 0"}}}