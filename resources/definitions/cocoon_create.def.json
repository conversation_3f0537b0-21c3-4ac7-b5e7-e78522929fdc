{"version": 2, "name": "Cocoon Create", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON><PERSON>", "manufacturer": "Cocoon Create", "file_formats": "text/x-gcode", "platform": "wanh<PERSON>_200_200_platform.obj", "has_materials": true, "machine_extruder_trains": {"0": "cocoon_create_extruder_0"}, "platform_offset": [0, -28, 0], "platform_texture": "Cocoon-backplate.png", "preferred_quality_type": "fine"}, "overrides": {"layer_height": {"default_value": 0.1}, "layer_height_0": {"default_value": 0.2}, "machine_depth": {"default_value": 200}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off \n G91 ;relative positioning\n G1 E-1 F300  ;retract the filament a bit before lifting the nozzle, to release some of the pressure\n G1 Z+0.5 E-5 X-20 Y-20 F{speed_travel} ;move Z up a bit and retract filament even more\n G28 X0 Y0 ;move X/Y to min endstops, so the head is out of the way\n M84 ;steppers off\n G90 ;absolute positioning"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 180}, "machine_name": {"default_value": "Cocoon Create"}, "machine_start_gcode": {"default_value": "G21 ;metric values\n G90 ;absolute positioning\n M82 ;set extruder to absolute mode\n M107 ;start with the fan off\n G28 X0 Y0 ;move X/Y to min endstops\n G28 Z0 ;move Z to min endstops\n G1 Z15.0 F{speed_travel} ;move the platform down 15mm\n G92 E0 ;zero the extruded length\n G1 F200 E3 ;extrude 3mm of feed stock\n G92 E0 ;zero the extruded length again\n G1 F{speed_travel} \n ;Put printing message on LCD screen\n M117 Printing..."}, "machine_width": {"default_value": 200}, "material_diameter": {"default_value": 1.75}, "retraction_amount": {"default_value": 4.5}, "retraction_enable": {"default_value": true}, "retraction_speed": {"default_value": 25}, "speed_print": {"default_value": 50}, "support_enable": {"default_value": true}, "top_bottom_thickness": {"default_value": 0.6}, "wall_thickness": {"value": "0.8"}}}