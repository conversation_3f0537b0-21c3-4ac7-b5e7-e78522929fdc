{"version": 2, "name": "Alya", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Alya", "manufacturer": "<PERSON><PERSON>", "file_formats": "text/x-gcode", "platform": "alya_platform.3mf", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "generic_abs", "generic_cpe", "generic_hips", "generic_nylon", "generic_pc", "generic_petg", "generic_pva", "generic_tpu", "innofill_innoflex60", "verbatim_bvoh"], "has_machine_quality": true, "has_materials": true, "has_variants": false, "machine_extruder_trains": {"0": "alya3dp_extruder_0"}, "platform_offset": [-60, -45, 75], "preferred_material": "generic_pla", "supports_usb_connection": false}, "overrides": {"adhesion_type": {"default_value": "raft", "options": {"brim": "Brim", "none": "None", "raft": "Raft"}}, "gantry_height": {"value": "55"}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 100}, "machine_end_gcode": {"default_value": ";End GCode\nM104 S0 ;extruder heater off \nM140 S0 ;heated bed heater off (if you have it)\nG91 ;relative positioning\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 X-20 Y-20 F{speed_travel} ;move Z up a bit and retract filament even more\nG28 X0 Y0 ;move X/Y to min endstops, so the head is out of the way\nG28 Z0\nM84 ;steppers off\nG90 ;absolute positioning\n;{profile_string}"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[75, 18], [18, 18], [18, 35], [75, 35]]}, "machine_heated_bed": {"default_value": false}, "machine_height": {"default_value": 133}, "machine_name": {"default_value": "ALYA 3DP"}, "machine_start_gcode": {"default_value": ";Sliced at: {day} {date} {time} \n ;Basic settings: Layer height: {layer_height} Walls: {wall_thickness} Fill: {fill_density} \n ;Print time: {print_time} \n ;Filament used: {filament_amount}m {filament_weight}g \n ;Filament cost: {filament_cost} \n G28 X0 Y0  ;move X Y to endstops \n G28 Z0     ;move Z to endstops \n ; M190 S{material_bed_temperature} ;bed temp \n M107 ; switch fan off \n M109 S{material_print_temperature} ;extruder temp set \n G1 F3000 \n G1 Z10 \n G92 E0                  ;zero the extruded length \n G1 F200 E1 ;extrude 1mm of feed stock \n G92 E0 ;zero the extruded length again \n  G4 P7000 ; wait 7000ms \n M117 Printing... ;Put printing message on LCD screen"}, "machine_width": {"default_value": 100}, "retraction_amount": {"default_value": 1.5}, "support_enable": {"default_value": true}}}