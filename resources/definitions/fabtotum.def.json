{"version": 2, "name": "Fabtotum Personal Fabricator", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Fab<PERSON><PERSON>", "manufacturer": "Fab<PERSON><PERSON>", "file_formats": "text/x-gcode", "platform": "fabtotum_platform.3mf", "has_machine_quality": true, "has_variants": true, "machine_extruder_trains": {"0": "fabtotum_extruder_0"}, "preferred_material": "fabtotum_pla", "preferred_variant_name": "Lite 0.4 mm", "supports_usb_connection": false, "variants_name": "Head"}, "overrides": {"gantry_height": {"value": "55"}, "infill_sparse_density": {"default_value": 24}, "machine_acceleration": {"default_value": 4000}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 234}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off\nM140 S0 ;heated bed heater off (if you have it)\nG91 ;relative positioning\nG1 E-1 F300    ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-3 X+5 Y+5 F5000 ;move Z up a bit and retract filament even more\n;end of the print\nM84 ;steppers off\nG90 ;absolute positioning\nM300 S2 ;FAB bep bep (end print)"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-75, 35], [-75, -18], [18, 35], [18, -18]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 241.5}, "machine_max_acceleration_e": {"default_value": 100}, "machine_max_acceleration_x": {"default_value": 10000}, "machine_max_acceleration_y": {"default_value": 10000}, "machine_max_acceleration_z": {"default_value": 50}, "machine_max_feedrate_x": {"default_value": 250}, "machine_max_feedrate_y": {"default_value": 250}, "machine_max_feedrate_z": {"default_value": 15}, "machine_max_jerk_e": {"default_value": 1.0}, "machine_max_jerk_xy": {"default_value": 25.0}, "machine_max_jerk_z": {"default_value": 0.4}, "machine_name": {"default_value": "FABtotum Personal Fabricator"}, "machine_start_gcode": {"default_value": ";Layer height: {layer_height}\n;Walls: {wall_thickness}\n;Fill: {infill_sparse_density}\n;Top\\Bottom Thickness: {top_bottom_thickness}\nG90 ;absolute positioning\nM82 ;set extruder to absolute mode\nG4 S1 ;1 millisecond pause to buffer the bep bep \nM300 S2 ;FAB bep bep (start the print, go check the oozing and skirt lines adesion) \nG4 S1 ;1 second pause to reach the printer (run fast)\nG92 E0 ;zero the extruded length \nG1 F200 E35    ;slowly extrude 35mm of filament to clean the nozzle and build up extrusion pressure \nG92 E0 ;zero the extruded length again \n;print"}, "machine_width": {"default_value": 214}, "material_final_print_temperature": {"value": "material_print_temperature - 5"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "retraction_hop_enabled": {"default_value": false}, "skirt_brim_minimal_length": {"default_value": 150}, "skirt_brim_speed": {"value": "speed_layer_0"}, "skirt_line_count": {"default_value": 3}, "speed_infill": {"value": "round(speed_print * 1.05, 0)"}, "speed_layer_0": {"value": "min(round(speed_print * 0.75, 0), 45.0)"}, "speed_topbottom": {"value": "round(speed_print * 0.95, 0)"}, "speed_travel": {"value": 200}, "speed_travel_layer_0": {"value": "round(speed_travel * 0.7, 0)"}, "speed_wall": {"value": "speed_print"}, "speed_wall_0": {"value": "round(speed_print * 0.9, 0)"}, "speed_wall_x": {"value": "speed_wall"}, "support_interface_enable": {"default_value": true}, "support_z_distance": {"default_value": 0.2, "value": "min(2 * layer_height, machine_nozzle_size * 0.75)"}, "top_bottom_thickness": {"default_value": 0.6}, "travel_avoid_distance": {"value": 1}}}