{"version": 2, "name": "anet3d", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "<PERSON>.He", "manufacturer": "<PERSON><PERSON>", "file_formats": "text/x-gcode", "first_start_actions": ["MachineSettingsAction"], "machine_extruder_trains": {"0": "anet3d_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality_type": "standard", "preferred_variant_name": "0.4mm Nozzle"}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_print": {"value": 1000}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": 1000}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "extruder_prime_pos_x": {"maximum_value": "machine_width", "minimum_value": "0"}, "extruder_prime_pos_y": {"maximum_value": "machine_depth", "minimum_value": "0"}, "fill_outline_gaps": {"value": false}, "gantry_height": {"value": "0"}, "infill_before_walls": {"value": true}, "infill_overlap": {"value": 30.0}, "infill_pattern": {"value": "'lines' if infill_sparse_density > 50 else 'cubic'"}, "infill_wipe_dist": {"value": 0}, "jerk_enabled": {"value": false}, "jerk_print": {"value": 30.0}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 500}, "machine_center_is_zero": {"default_value": false}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 500}, "machine_max_acceleration_y": {"value": 500}, "machine_max_acceleration_z": {"value": 100}, "machine_max_feedrate_e": {"value": 50}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 10}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.4}, "machine_use_extruder_offset_to_offset_coords": {"default_value": true}, "material_bed_temperature": {"minimum_value": "0"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "material_print_temperature": {"minimum_value": "0"}, "material_standby_temperature": {"minimum_value": "0"}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "optimize_wall_printing_order": {"value": true}, "relative_extrusion": {"enabled": false, "value": false}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 10}, "retraction_hop": {"value": 1}, "retraction_min_travel": {"value": 1.5}, "retraction_prime_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_retract_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "skin_overlap": {"value": 10.0}, "skirt_brim_speed": {"value": "speed_layer_0"}, "speed_infill": {"value": "speed_print * 2"}, "speed_layer_0": {"value": "speed_print / 2"}, "speed_prime_tower": {"value": "speed_print"}, "speed_print": {"value": 50.0}, "speed_roofing": {"value": "speed_topbottom"}, "speed_support": {"value": "speed_print"}, "speed_support_interface": {"value": "speed_print"}, "speed_travel": {"value": "150.0 if speed_print < 60 else 250.0 if speed_print > 100 else speed_print * 2.5"}, "speed_travel_layer_0": {"value": "100 if speed_layer_0 < 20 else 150 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"value": 5}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3"}, "travel_avoid_other_parts": {"value": true}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0.2}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_corner": {"value": "'z_seam_corner_weighted'"}, "z_seam_type": {"value": "'back'"}}}