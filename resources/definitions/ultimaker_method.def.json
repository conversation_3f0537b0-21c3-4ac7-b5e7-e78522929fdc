{"version": 2, "name": "UltiMaker Method", "inherits": "ultimaker_method_base", "metadata": {"visible": true, "author": "UltiMaker", "manufacturer": "Ultimaker B.V.", "file_formats": "application/x-makerbot", "platform": "ultimaker_method_platform.stl", "exclude_materials": ["dsm_", "Essentium_", "imade3d_", "chromatik_", "3D-Fuel_", "bestfilament_", "eazao_", "emotiontech_", "eryone_", "eSUN_", "Extrudr_", "fabtotum_", "fdplast_", "filo3d_", "generic_", "goofoo_", "ideagen3D_", "imade3d_", "innofill_", "layer_one_", "leapfrog_", "polyflex_pla", "polymax_pla", "polymaker_polymax_pc_175", "polyplus_pla", "polywood_pla", "redd_", "tizyx_", "ultimaker_asa_175", "ultimaker_abs_175", "ultimaker_absr_175", "ultimaker_abscf_175", "ultimaker_bvoh_175", "ultimaker_cffpa_175", "ultimaker_cpe_175", "ultimaker_nylon_175", "ultimaker_hips_175", "ultimaker_pc_175", "ultimaker_tpu_175", "ultimaker_rapidrinse_175", "ultimaker_sr30", "ultimaker_metallic_pla_175", "verbatim_", "Vertex_", "volumic_", "xyzprinting_", "zyyx_pro_", "octofiber_", "fiberlogy_", "ultimaker_pc-abs", "ultimaker_pc-abs-fr", "ultimaker_metallic_pla_175"], "has_machine_materials": true, "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "ultimaker_method_extruder_left", "1": "ultimaker_method_extruder_right"}, "platform_offset": [0, 0, 16], "preferred_material": "ultimaker_pla_175", "preferred_quality_type": "draft", "preferred_variant_name": "1A", "reference_machine_id": "fire_e", "supports_network_connection": true, "supports_usb_connection": false, "variant_definition": "ultimaker_method", "variants_name": "Extruder", "variants_name_has_translation": true, "weight": -1}, "overrides": {"build_volume_temperature": {"maximum_value": "67"}, "machine_name": {"default_value": "UltiMaker Method"}, "prime_tower_position_x": {"value": "(150 / 2 + resolveOrValue('prime_tower_size') / 2) if resolveOrValue('machine_shape') == 'elliptic' else (150 - (resolveOrValue('prime_tower_base_size') if (resolveOrValue('adhesion_type') == 'raft' or resolveOrValue('prime_tower_brim_enable')) else 0) - max(max(extruderValues('travel_avoid_distance')) + max(extruderValues('support_offset')) + (extruderValue(skirt_brim_extruder_nr, 'skirt_brim_line_width') * extruderValue(skirt_brim_extruder_nr, 'skirt_line_count') * extruderValue(skirt_brim_extruder_nr, 'initial_layer_line_width_factor') / 100 + extruderValue(skirt_brim_extruder_nr, 'skirt_gap') if resolveOrValue('adhesion_type') == 'skirt' else 0) + (resolveOrValue('draft_shield_dist') if resolveOrValue('draft_shield_enabled') else 0), max(map(abs, extruderValues('machine_nozzle_offset_x'))), 1)) - (150 / 2 if resolveOrValue('machine_center_is_zero') else 0)"}, "prime_tower_position_y": {"value": "190 - prime_tower_size - (resolveOrValue('prime_tower_base_size') if (resolveOrValue('adhesion_type') == 'raft' or resolveOrValue('prime_tower_brim_enable')) else 0) - max(max(extruderValues('travel_avoid_distance')) + max(extruderValues('support_offset')) + (extruderValue(skirt_brim_extruder_nr, 'skirt_brim_line_width') * extruderValue(skirt_brim_extruder_nr, 'skirt_line_count') * extruderValue(skirt_brim_extruder_nr, 'initial_layer_line_width_factor') / 100 + extruderValue(skirt_brim_extruder_nr, 'skirt_gap') if resolveOrValue('adhesion_type') == 'skirt' else 0) + (resolveOrValue('draft_shield_dist') if resolveOrValue('draft_shield_enabled') else 0), max(map(abs, extruderValues('machine_nozzle_offset_y'))), 1) - (190 / 2 if resolveOrValue('machine_center_is_zero') else 0)"}}}