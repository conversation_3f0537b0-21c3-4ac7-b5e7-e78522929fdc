{"version": 2, "name": "fusedform_doppia_base", "inherits": "fusedform_base", "metadata": {"visible": false, "author": "<PERSON>", "manufacturer": "Fused Form", "exclude_materials": ["structur3d_dap100silicone"], "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "fusedform_doppia_base_extruder_0", "1": "fusedform_doppia_base_extruder_1"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal"}, "overrides": {"adhesion_type": {"default_value": "skirt"}, "cool_fan_enabled": {"default_value": true}, "cool_fan_full_at_height": {"value": 0.4}, "cool_fan_full_layer": {"value": 2}, "cool_min_speed": {"value": 30}, "gantry_height": {"value": "70"}, "infill_overlap": {"value": 0}, "infill_sparse_density": {"value": 15}, "layer_height": {"default_value": 0.15}, "layer_height_0": {"default_value": 0.2}, "machine_center_is_zero": {"default_value": false}, "machine_end_gcode": {"value": "'M104 S0 ;extruder heater off' + ('\\nM140 S0 ;heated bed heater off' if machine_heated_bed else '') + '\\nG91 ;relative positioning\\nG1 E-1 F300  ;retract the filament a bit before lifting the nozzle, to release some of the pressure\\nG28 X0 Y0 ;move X/Y to min endstops, so the head is out of the way\\nM84 ;steppers off\\nG90 ;absolute positioning\\nM107 ; Fans off'"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-20, 20], [-20, -20], [18, 20], [18, -18]]}, "machine_heated_bed": {"default_value": true}, "machine_start_gcode": {"default_value": "G21 ;metric values\nG90 ;absolute positioning\nM82 ;set extruder to absolute mode\nM107 ;start with the fan off\nG28 X0 Y0 ;move X/Y to min endstops\nG28 Z0 ;move Z to min endstops\nG1 Z15.0 F9000 ;move the platform down 15mm\nG92 E0 ;zero the extruded length\nG1 F200 E6 ;extrude 6 mm of feed stock\nG92 E0 ;zero the extruded length again\n;Put printing message on LCD screen\nM117 Printing..."}, "machine_use_extruder_offset_to_offset_coords": {"default_value": true}, "optimize_wall_printing_order": {"value": true}, "retraction_amount": {"default_value": 4}, "retraction_count_max": {"default_value": 10}, "retraction_enable": {"default_value": true}, "retraction_extrusion_window": {"value": 4}, "retraction_hop": {"default_value": 0.2}, "retraction_hop_enabled": {"value": true}, "retraction_min_travel": {"value": 5}, "retraction_speed": {"default_value": 70}, "speed_infill": {"value": 45}, "speed_print": {"value": 45}, "speed_topbottom": {"value": 40}, "speed_travel": {"value": 75}, "speed_wall": {"value": 35}, "speed_wall_x": {"value": 40}, "speed_z_hop": {"value": 2.5}, "support_angle": {"default_value": 50}, "support_brim_enable": {"value": true}, "support_enable": {"value": true}, "support_infill_angles": {"value": [-45]}, "support_interface_density": {"value": 70}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": 0.5}, "support_interface_pattern": {"default_value": "lines"}, "support_pattern": {"default_value": "lines"}, "support_xy_distance": {"value": 0.5}, "support_z_distance": {"value": 0.3}, "switch_extruder_retraction_amount": {"value": 6}, "switch_extruder_retraction_speeds": {"value": 60}, "top_bottom_thickness": {"value": 1.5}, "wall_line_count": {"value": 3}, "wall_thickness": {"value": 1.2}}}