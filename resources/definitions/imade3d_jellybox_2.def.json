{"version": 2, "name": "Imade3D JellyBOX 2", "inherits": "imade3d_jellybox_root", "metadata": {"visible": true, "author": "Imade3D", "platform": "imade3d_jellybox_2_platform.3mf", "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "imade3d_jellybox_2_extruder_0"}, "platform_offset": [0, -10, 0], "preferred_quality_type": "fast", "preferred_variant_name": "0.4 mm"}, "overrides": {"gradual_infill_step_height": {"default_value": 3}, "gradual_infill_steps": {"default_value": 0}, "machine_depth": {"default_value": 165}, "machine_end_gcode": {"default_value": "\n;---------------------------------\n;;; Jellybox End Script Begin ;;;\n;_________________________________\n; end gcode last modified Nov 30, 2018\nM117 Finishing Up ;write Finishing Up\n\nM107        ;turn the fan off\nM104 S0     ;extruder heater off\nM140 S0     ;bed heater off (if you have it)\nG91         ;relative positioning (includes extruder)\nG1 E-1 F2500 ;retract the filament a bit before lifting the nozzle to release some of the pressure\nG1 Z0.5 E-4 X-10 F9000 ;get out and retract filament even more\nG1 E-25 F2500 ;retract even more\nG90         ;absolute positioning (includes extruder)\nG28 X       ;home X so the head is out of the way\nG1 Y140     ;move Y forward, so the print is more accessible\nM84         ;steppers off\n\nM117 Print finished ;write Print finished\n;---------------------------------------\n;;; Jellybox End Script End ;;;\n;_______________________________________"}, "machine_head_with_fans_polygon": {"default_value": [[0, 0], [0, 0], [0, 0], [0, 0]]}, "machine_height": {"default_value": 145}, "machine_name": {"default_value": "IMADE3D JellyBOX 2"}, "machine_nozzle_size": {"default_value": 0.4}, "machine_start_gcode": {"default_value": ";---------------------------------------\n; ; ; Jellybox Start Script Begin ; ; ;\n;_______________________________________\n; for slicer: CURA 3\n; start gcode last modified Jun 1, 2019\n\n;              Print Settings Summary\n; (leave these alone: this is only a list of the slicing settings)\n; (overwriting these values will NOT change your printer's behavior)\n;             sliced for :  {machine_name}\n;                jobname :  {jobname}\n;        gcode generated :  {day}, {date}, {time}\n;        est. print time :  {print_time}\n;        nozzle diameter :  {machine_nozzle_size}\n;      filament diameter :  {material_diameter}\n;           layer height :  {layer_height}\n;       1st layer height :  {layer_height_0}\n;             line width :  {line_width}  \n;  outer wall wipe dist. :  {wall_0_wipe_dist}\n;      infill line width :  {infill_line_width}\n;         wall thickness :  {wall_thickness}\n;          top thickness :  {top_thickness}\n;       bottom thickness :  {bottom_thickness}\n;         infill density :  {infill_sparse_density}\n;         infill pattern :  {infill_pattern}\n;      print temperature :  {material_print_temperature}\n;  1st layer print temp. :  {material_print_temperature_layer_0}\n; heated bed temperature :  {material_bed_temperature}\n;    1st layer bed temp. :  {material_bed_temperature_layer_0}\n;      regular fan speed :  {cool_fan_speed_min}\n;          max fan speed :  {cool_fan_speed_max}\n;     retraction amount  :  {retraction_amount}\n;   retr. retract speed  :  {retraction_retract_speed}\n;     retr. prime speed  :  {retraction_prime_speed}\n;   build plate adhesion :  {adhesion_type}\n;                support ?  {support_enable}\n;             spiralized ?  {magic_spiralize}\n\nG21               ;metric values\nG90               ;absolute positioning\nM82               ;set extruder to absolute mode\nM107              ;start with the fan off\nM117 Preparing    ;write Preparing\nM190 S{material_bed_temperature_layer_0}              ;wait for the bed to reach desired temperature\nM109 S180         ;wait for the extruder to reach 180C\nG28               ;home all axes\nM203 Z4           ;slow Z speed down for greater accuracy when probing\nG29 O             ;run auto bed leveling procedure IF leveling not active already\n; M500            ;optionally save the mesh\nM203 Z7           ;pick up z speed again for printing\nG28 X             ;home x to get as far from the plate as possible\nM420 S1           ;(re) enable bed leveling if turned off by the G28\nG0 Y0 F5000       ;position Y in front\nG0 Z15 F3000      ;position Z\nM109 S{material_print_temperature_layer_0}             ;wait for the extruder to reach desired temperature\nM300 S440 P300    ;play a tone\n; M0 Ready! Click to start ; optionally, stop and wait for user to continue\nM420 S1           ;(re) enable bed leveling to make iron-sure\nM117 Print starting   ;write Print starting\n;================ ;PRINT:LINE start\nG90               ;absolute positioning\nG92 E0            ;reset the extruder position\nM420 S1           ;(re) enable bed leveling to make iron-sure\nG0 Z0             ;get Z down\nM83               ;relative extrusion mode\nM420 S1           ;(re) enable bed leveling to make iron-sure\nG1 E20 F300       ;extrude __mm of feed stock\nG1 E18 F250       ;extrude __mm of feed stock\nG1 E10 F250       ;extrude __mm of feed stock\nG4 S2             ;pause for ooze\nM400              ;make sure all is finished\nM420 S1           ;(re) enable bed leveling to make iron-sure\nG0 F500 X3 Y0 Z0.3;get to the start of the LINE\nG1 E2 F300        ;extrude __mm of feed stock\nG1 F1000 X152 E7  ;print a thick LINE extruding __mm along the way\nG92 E0            ;reset the extruder position\n;---------------------------------------------\n; ; ; Jellybox Printer Start Script End ; ; ;\n;_____________________________________________\n"}, "machine_width": {"default_value": 180}, "retraction_prime_speed": {"value": "max(retraction_speed - 30, 5)"}}}