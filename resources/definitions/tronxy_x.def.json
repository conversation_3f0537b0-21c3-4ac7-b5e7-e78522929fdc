{"version": 2, "name": "Tronxy Base Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "KV/AdderMk2", "manufacturer": "Tronxy", "file_formats": "text/x-gcode", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "tronxy_base_extruder_0", "1": "tronxy_base_extruder_1"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal", "preferred_variant_name": "0.4mm Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"acceleration_enabled": {"value": false}, "acceleration_print": {"value": "machine_acceleration"}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": "machine_acceleration"}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "brim_replaces_support": {"value": false}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "fill_outline_gaps": {"value": true}, "infill_before_walls": {"value": false}, "infill_overlap": {"value": 30.0}, "infill_pattern": {"value": "'lines' if infill_sparse_density > 56 else 'cubic'"}, "infill_wipe_dist": {"value": 0.0}, "jerk_enabled": {"value": false}, "jerk_print": {"value": 12}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_end_gcode": {"default_value": "M107 T0\nM104 S0\nM104 S0 T1\nM140 S0\nG92 E0\nG91\nG1 E-1 F300 \nG1 Z+0.5 E-5 X-20 Y-20 F9000\nG28 X0 Y0\nM84 ;steppers off\nG90 ;absolute positioning\n"}, "machine_heated_bed": {"default_value": true}, "machine_name": {"default_value": "Tronxy Base Printer"}, "machine_start_gcode": {"default_value": "G21\nG90\nM82\nM107 T0\nM140 S{material_bed_temperature_layer_0}\nM104 S{material_print_temperature_layer_0} T0\nM190 S{material_bed_temperature_layer_0}\nM109 S{material_print_temperature_layer_0} T0\nG28\nG92 E0\nG1 Z15.0 F{speed_travel_layer_0}\nG0 E3 F200\nG92 E0\n"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_flow": {"value": 95}, "material_flow_layer_0": {"value": 95}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "2 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": "True"}, "retract_at_layer_change": {"value": true}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 5}, "retraction_hop": {"value": 0.2}, "retraction_hop_enabled": {"value": true}, "retraction_min_travel": {"value": 1.5}, "retraction_prime_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e"}, "retraction_retract_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e"}, "retraction_speed": {"default_value": 45, "maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e"}, "skin_overlap": {"value": 10.0}, "skirt_brim_speed": {"value": "speed_layer_0"}, "skirt_gap": {"value": 10.0}, "skirt_line_count": {"value": 3}, "speed_layer_0": {"value": 30.0}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": 60.0}, "speed_roofing": {"value": "speed_topbottom"}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "speed_print * 0.5"}, "speed_travel": {"value": "60.0 if speed_print < 50 else 120.0 if speed_print > 80 else speed_print * 1.25"}, "speed_travel_layer_0": {"value": "45 if speed_layer_0 < 20 else 60 if speed_layer_0 > 30 else speed_layer_0 * 1.5"}, "speed_wall": {"value": "speed_print * 0.75"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"value": 5}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width/2.0/layer_height)))"}, "support_brim_enable": {"value": true}, "support_brim_width": {"value": 4}, "support_infill_angles": {"value": [45]}, "support_infill_rate": {"value": "0 if support_enable and support_structure == 'tree' else 30"}, "support_interface_density": {"value": 33.333}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"value": "'grid'"}, "support_pattern": {"value": "'zigzag'"}, "support_wall_count": {"value": "1 if (support_structure == 'tree') else 0"}, "support_xy_distance": {"value": "wall_line_width_0 * 3"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height*2"}, "top_bottom_pattern": {"value": "'zigzag'"}, "top_bottom_pattern_0": {"value": "'zigzag'"}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3"}, "travel_avoid_other_parts": {"value": false}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0.0}, "wall_line_count": {"value": "3"}, "z_seam_corner": {"value": "'z_seam_corner_inner'"}, "z_seam_type": {"value": "'sharpest_corner'"}}}