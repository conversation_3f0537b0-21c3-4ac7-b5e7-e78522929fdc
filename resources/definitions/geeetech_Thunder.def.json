{"version": 2, "name": "Geeetech Thunder", "inherits": "Geeetech_Base_Single_Extruder", "metadata": {"visible": true, "machine_extruder_trains": {"0": "Geeetech_Single_Extruder"}}, "overrides": {"acceleration_print": {"value": 3500}, "acceleration_travel": {"value": 5000}, "cool_fan_full_at_height": {"label": "Regular Fan Speed at Height", "value": "layer_height_0 +  layer_height"}, "cool_fan_speed": {"maximum_value": "151", "value": "151 if speed_infill>=200  else 100"}, "cool_fan_speed_max": {"maximum_value": "151", "value": "cool_fan_speed"}, "cool_fan_speed_min": {"maximum_value": "151", "value": "cool_fan_speed"}, "cool_min_layer_time": {"value": "1.3 if speed_infill>=200  else 2.5"}, "fill_outline_gaps": {"label": "Print Thin Walls", "value": false}, "gantry_height": {"value": 35}, "infill_before_walls": {"label": "Infill Before Walls", "value": false}, "infill_overlap": {"label": "Infill Overlap Percentage", "value": 10.0}, "infill_sparse_density": {"label": "Infill Density", "value": "15"}, "infill_wipe_dist": {"label": "Infill Wipe Distance", "value": 0.0}, "line_width": {"value": "1.2*machine_nozzle_size if speed_infill>=200 else machine_nozzle_size"}, "machine_depth": {"default_value": 250}, "machine_end_gcode": {"default_value": "G91 ;Switch to relative positioning\nG1 E-2.5 F2700 ;Retract filament\nG1 E-1.5 Z0.2 F2400 ;Retract and raise Z\nG1 X5 Y5 F3000 ;Move away\nG1 Z10 ;lift print head\nG90 ;Switch to absolute positioning\nG28 X Y ;homing XY\nM106 S0 ;off Fan\nM104 S0 ;Cooldown hotend\nM140 S0 ;Cooldown bed\nM84 X Y E ;Disable steppers"}, "machine_head_with_fans_polygon": {"default_value": [[-20, 25], [20, 25], [20, -25], [-20, -25]]}, "machine_height": {"default_value": 260}, "machine_max_acceleration_e": {"value": 3500}, "machine_max_acceleration_x": {"value": 5000}, "machine_max_acceleration_y": {"value": 4000}, "machine_max_acceleration_z": {"value": 50}, "machine_max_feedrate_e": {"value": 60}, "machine_max_feedrate_x": {"value": 300}, "machine_max_jerk_e": {"value": 8}, "machine_max_jerk_xy": {"value": 45}, "machine_max_jerk_z": {"value": 0.8}, "machine_name": {"default_value": "Geeetech Thunder"}, "machine_start_gcode": {"default_value": ";Official wiki URL for Thunder:https://www.geeetech.com/wiki/index.php/Geeetech_Thunder_3D_printer  \n\nM104 S{material_print_temperature_layer_0} ; Set Hotend Temperature\nM190 S{material_bed_temperature_layer_0} ; Wait for Bed Temperature\nM109 S{material_print_temperature_layer_0} ; Wait for Hotend Temperature\nG92 E0 ; Reset Extruder\nG28 ; Home all axes\nM107 P0 ;Off Main Fan\nM107 P1 ;Off Aux Fan\nM2012 P8 S1 F100 ; ON Light\n;M106 P0 S383 ; ON MainFan 150% if need\n;M106 P1 S255 ; ON Aux Fan 100% if need\nG1 Z5.0 F3000 ;Move Z Axis up little to prevent scratching of Heat Bed\nG1 X0.1 Y20 Z0.8 F5000 ; Move to start position\nG1 X0.1 Y200.0 Z1.2 F1500 E30 ; Draw the first line\nG92 E0 ; Reset Extruder\nG1 X0.4 Y200.0 Z1.2 F3000 ; Move to side a little\nG1 X0.4 Y20 Z1.2 F1500 E25 ; Draw the second line\nG92 E0 ; Reset Extruder\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X5 Y20 Z0.4 F3000.0 ; Scrape off nozzle residue"}, "machine_width": {"default_value": 250}, "material_flow_layer_0": {"value": 95}, "material_print_temperature": {"maximum_value": "250"}, "material_print_temperature_layer_0": {"maximum_value_warning": 300, "value": "material_print_temperature"}, "retraction_speed": {"value": 35}, "skin_overlap": {"label": "Skin Overlap Percentage", "value": 10.0}, "small_hole_max_size": {"value": "8 if speed_infill>=200  else 0"}, "speed_infill": {"maximum_value_warning": "300"}, "speed_print": {"maximum_value_warning": "300", "value": 250}, "speed_roofing": {"maximum_value_warning": "300"}, "speed_support": {"maximum_value_warning": "200"}, "speed_support_bottom": {"maximum_value_warning": "200"}, "speed_support_infill": {"maximum_value_warning": "200"}, "speed_support_interface": {"maximum_value_warning": "200"}, "speed_support_roof": {"maximum_value_warning": "200"}, "speed_topbottom": {"maximum_value_warning": "300"}, "speed_travel_layer_0": {"maximum_value_warning": "200"}, "speed_wall": {"maximum_value_warning": "300"}, "speed_wall_0": {"maximum_value_warning": "300"}, "speed_wall_x": {"maximum_value_warning": "300"}, "wall_0_wipe_dist": {"label": "Outer Wall Wipe Distance", "value": 0.0}, "zig_zaggify_infill": {"label": "Connect Infill Lines", "value": true}}}