{"version": 2, "name": "Vertex Delta K8800", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Velleman N.V.", "manufacturer": "Velleman N.V.", "file_formats": "text/x-gcode", "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "vertex_delta_k8800_extruder_0"}}, "overrides": {"brim_width": {"value": 6}, "cool_fan_full_at_height": {"value": 2}, "cool_fan_full_layer": {"value": 11}, "cool_min_layer_time": {"value": 8}, "gantry_height": {"value": "0"}, "infill_line_width": {"value": 0.35}, "infill_overlap": {"value": 5}, "infill_sparse_density": {"value": 40}, "line_width": {"value": 0.35}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 200}, "machine_end_gcode": {"default_value": "; Vertex Delta end code\nM107 ; Turn off fan\nG91 ; Relative positioning\nT0\nG1 E-1 F1500; Reduce filament pressure\nM104 T0 S0\nG90 ; Absolute positioning\nG92 E0 ; Reset extruder position\nM300 S4000 P500\nM300 S3000 P500\nM300 S2000 P800\nG28\nM84 ; Turn steppers off"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_height": {"default_value": 225}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": "; Vertex Delta Start Gcode\nM0 Is my nozzle clean?\nM400\nG28 ; Home extruder\nM106 S128 ; Start fan\nM104 T0 R130 ; Set cold nozzle\nM109 T0 R130 ; Wait for cold nozzle\nM117 Leveling bed...\nG29 ; Level Bed\nG1 X0 Y100 Z1 F2000\nG92 Z0.9 ; Set Z position (SET Z OFFSET HERE -> 1 - OFFSET)\nM107 ; Stop fan\nG90 ; Absolute positioning\nM82 ; Extruder in absolute mode\nM104 T0 S{material_print_temperature}\nG92 E0 ; Reset extruder position\nM109 T0 S{material_print_temperature}\nM117 Priming nozzle...\nM83\nG1 E20 F100 ; purge/prime nozzle\nM82\nG92 E0 ; Reset extruder position\nG4 S3 ; Wait 3 seconds\nG1 Z5 F2000\nM117 Vertex Delta printing"}, "machine_width": {"default_value": 200}, "min_infill_area": {"value": 0.1}, "retract_at_layer_change": {"value": true}, "retraction_count_max": {"value": 15}, "retraction_extrusion_window": {"value": 1}, "retraction_hop": {"value": 0.1}, "retraction_hop_enabled": {"value": true}, "retraction_min_travel": {"value": 1}, "skirt_brim_minimal_length": {"value": 50}, "skirt_brim_speed": {"value": 20}, "skirt_line_count": {"value": 2}, "speed_infill": {"value": 40}, "speed_layer_0": {"value": 20}, "speed_print": {"value": 35}, "speed_print_layer_0": {"value": 20}, "speed_topbottom": {"value": 35}, "speed_travel": {"value": 190}, "speed_wall": {"value": 35}, "speed_wall_x": {"value": 35}, "support_xy_distance": {"value": 1}, "support_z_distance": {"value": 0.4}, "top_bottom_thickness": {"value": 0.6}, "travel_retract_before_outer_wall": {"value": false}, "wall_thickness": {"value": "0.7"}}}