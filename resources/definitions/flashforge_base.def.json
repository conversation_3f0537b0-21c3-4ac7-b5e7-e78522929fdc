{"version": 2, "name": "Dreamer NX", "inherits": "fdmprinter", "machine_end_gcode": {"default_value": ";end gcode\nM104 S0 T0\nM140 S0 T0\nG162 Z F1800\nG28 X Y\nM652\nM132 X Y Z A B\nG91\nM18"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_start_gcode": {"default_value": ";Start Gcode\nG90 ;absolute positioning\nM118 X25.00 Y25.00 Z20.00 T0\nM140 S{material_bed_temperature_layer_0} T0 ;Heat bed up to first layer temperature\nM104 S{material_print_temperature_layer_0} T0 ;Set nozzle temperature to first layer temperature\nM107 ;start with the fan off\nG90\nG28\nM132 X Y Z A B\nG1 Z50.000 F420\nG161 X Y F3300\nM7 T0\nM6 T0\nM651\nM907 X100 Y100 Z40 A100 B20 ;Digital potentiometer value\nM108 T0\n;Purge line\nG1 X-110.00 Y-60.00 F4800\nG1 Z{layer_height_0} F420\nG1 X-110.00 Y60.00 E17,4 F1200\n;Purge line end"}, "metadata": {"visible": false, "author": "Egon", "manufacturer": "Flashforge", "file_formats": "text/x-gcode", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "flashforge_dreamernx_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal", "preferred_variant_name": "0.4mm Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"machine_acceleration": {"value": 500}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 1500}, "machine_max_acceleration_y": {"value": 1500}, "machine_max_acceleration_z": {"value": 500}, "machine_max_feedrate_e": {"value": 50}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 10}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.4}}}