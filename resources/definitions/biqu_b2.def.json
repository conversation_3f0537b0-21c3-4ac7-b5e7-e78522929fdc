{"version": 2, "name": "Biqu B2", "inherits": "biqu_b1", "metadata": {"visible": true, "author": "<PERSON>", "has_textured_buildplate": true, "machine_extruder_trains": {"0": "biqu_b2_extruder_0", "1": "biqu_b2_extruder_1"}, "quality_definition": "biqu_b2"}, "overrides": {"gantry_height": {"value": 27.5}, "machine_end_gcode": {"default_value": ";BEGIN OF CUSTOM END GCODE\nM104 S0\nM140 S0\n;Retract the filament\nG91\nG1 E-30 F300\nG1 Z5\nG90\nG28 X0 Y{machine_depth}\n;END OF CUSTOM END GCODE"}, "machine_extruder_count": {"default_value": 2}, "machine_extruders_share_heater": {"default_value": true}, "machine_extruders_share_nozzle": {"default_value": true}, "machine_extruders_shared_nozzle_initial_retraction": {"default_value": 30}, "machine_head_with_fans_polygon": {"default_value": [[-33, 35], [-33, -23], [33, -23], [33, 35]]}, "machine_name": {"default_value": "BIQU B2"}, "machine_start_gcode": {"default_value": ";BEGIN OF CUSTOM START GCODE\nG28 ;Home\nG1 Z15.0 F6000 ;Move the platform down 15mm\n;Prime the extruder\nM109 S{material_print_temperature_layer_0} ; Wait for Extruder temperature\nT0\nG92 E0\nG1 F1200 E-30\nG92 E0\nM109 S{material_print_temperature_layer_0} ; Wait for Extruder temperature\nT1\nG92 E0\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X6.1 Y20 Z0.3 F5000.0 ; Move to start position\nM117 Purging\nG1 X6.1 Y200.0 Z0.3 F1500.0 E10 ; Draw the first line\nG1 X6.4 Y200.0 Z0.3 F5000.0 ; Move to side a little\nG1 X6.4 Y20 Z0.3 F1500.0 E20 ; Draw the second line\nG1 X6.7 Y20 Z0.3 F5000.0 ; Move to side a little\nG1 X6.7 Y200.0 Z0.3 F1500.0 E30 ; Draw the three line\nG1 X7.0 Y200.0 Z0.3 F5000.0 ; Move to side a little\nG1 X7.0 Y20 Z0.3 F1500.0 E40 ; Draw the four line\nG1 X7.3 Y20 Z0.3 F5000.0 ; Move to side a little\nG1 X7.3 Y200.0 Z0.3 F1500.0 E50 ; Draw the four line\nG92 E0 \nT1\nG92 E0\nG1 F1200 E-30\nG92 E0\nT0\nG92 E0\nG1 F1200 E30\nG92 E0\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X9.1 Y20 Z0.3 F5000.0 ; Move to start position\nM117 Purging\nG1 X9.1 Y200.0 Z0.3 F1500.0 E10 ; Draw the first line\nG1 X9.4 Y200.0 Z0.3 F5000.0 ; Move to side a little\nG1 X9.4 Y20 Z0.3 F1500.0 E20 ; Draw the second line\nG1 X9.7 Y20 Z0.3 F5000.0 ; Move to side a little\nG1 X9.7 Y200.0 Z0.3 F1500.0 E30 ; Draw the three line\nG1 X10.0 Y200.0 Z0.3 F5000.0 ; Move to side a little\nG1 X10.0 Y20 Z0.3 F1500.0 E40 ; Draw the four line\nG1 X10.3 Y20 Z0.3 F5000.0 ; Move to side a little\nG1 X10.3 Y200.0 Z0.3 F1500.0 E50 ; Draw the four line\nT0\nG92 E0\nG1 F1200 E-30\nG92 E0\nG92 E0\nT{initial_extruder_nr} ; RESET EXTRUDER TO INITIAL\n; start print\n;END OF CUSTOM START GCODE"}, "prime_tower_enable": {"default_value": true}, "prime_tower_mode": {"default_value": "interleaved"}}}