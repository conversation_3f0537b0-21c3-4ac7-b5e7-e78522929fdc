{"version": 2, "name": "Extruder", "metadata": {"visible": false, "author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Unknown", "position": "0", "setting_version": 25, "type": "extruder"}, "settings": {"machine_settings": {"children": {"extruder_nr": {"default_value": "0", "description": "The extruder train used for printing. This is used in multi-extrusion.", "label": "Extruder", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": true, "settable_per_meshgroup": false, "type": "extruder"}, "extruder_prime_pos_z": {"default_value": 0, "description": "The Z coordinate of the position where the nozzle primes at the start of printing.", "label": "Extruder Prime Z Position", "maximum_value": "machine_height", "minimum_value_warning": "0", "settable_per_extruder": true, "settable_per_mesh": false, "type": "float", "unit": "mm"}, "machine_extruder_change_duration": {"default_value": 0, "description": "When using a multi tool setup, this value is the tool change time in seconds.  This value will be added to the estimate time based on the number of changes that occur.", "label": "Extruder Change duration", "minimum_value": "0", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float"}, "machine_extruder_cooling_fan_number": {"default_value": 0, "description": "The number of the print cooling fan associated with this extruder. Only change this from the default value of 0 when you have a different print cooling fan for each extruder.", "label": "Extruder Print Cooling Fan", "minimum_value": "0", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "int"}, "machine_extruder_end_code": {"default_value": "", "description": "End g-code to execute when switching away from this extruder.", "label": "Extruder End G-Code", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "str"}, "machine_extruder_end_code_duration": {"default_value": 0, "description": "The time it takes to execute the end g-code, when switching away from this extruder.", "label": "Extruder End G-Code Duration", "minimum_value": "0", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float"}, "machine_extruder_end_pos_abs": {"default_value": false, "description": "Make the extruder ending position absolute rather than relative to the last-known location of the head.", "label": "Extruder End Position Absolute", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "bool"}, "machine_extruder_end_pos_x": {"default_value": 0, "description": "The x-coordinate of the ending position when turning the extruder off.", "label": "Extruder End Position X", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float", "unit": "mm"}, "machine_extruder_end_pos_y": {"default_value": 0, "description": "The y-coordinate of the ending position when turning the extruder off.", "label": "Extruder End Position Y", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float", "unit": "mm"}, "machine_extruder_prestart_code": {"default_value": "", "description": "Prestart g-code to execute before switching to this extruder.", "label": "Extruder Prestart G-Code", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "str"}, "machine_extruder_start_code": {"default_value": "", "description": "Start g-code to execute when switching to this extruder.", "label": "Extruder Start G-Code", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "str"}, "machine_extruder_start_code_duration": {"default_value": 0, "description": "The time it'll take to execute the start g-code, when switching to this extruder.", "label": "Extruder Start G-Code Duration", "minimum_value": "0", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float"}, "machine_extruder_start_pos_abs": {"default_value": false, "description": "Make the extruder starting position absolute rather than relative to the last-known location of the head.", "label": "Extruder Start Position Absolute", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "bool"}, "machine_extruder_start_pos_x": {"default_value": 0, "description": "The x-coordinate of the starting position when turning the extruder on.", "label": "Extruder Start Position X", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float", "unit": "mm"}, "machine_extruder_start_pos_y": {"default_value": 0, "description": "The y-coordinate of the starting position when turning the extruder on.", "label": "Extruder Start Position Y", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float", "unit": "mm"}, "machine_nozzle_head_distance": {"default_value": 3, "description": "The height difference between the tip of the nozzle and the lowest part of the print head.", "label": "Nozzle Length", "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float", "unit": "mm"}, "machine_nozzle_id": {"default_value": "unknown", "description": "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\".", "label": "Nozzle ID", "settable_per_extruder": true, "settable_per_mesh": false, "type": "str"}, "machine_nozzle_offset_x": {"default_value": 0, "description": "The x-coordinate of the offset of the nozzle.", "label": "Nozzle X Offset", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float", "unit": "mm"}, "machine_nozzle_offset_y": {"default_value": 0, "description": "The y-coordinate of the offset of the nozzle.", "label": "Nozzle Y Offset", "settable_globally": false, "settable_per_extruder": true, "settable_per_mesh": false, "settable_per_meshgroup": false, "type": "float", "unit": "mm"}, "machine_nozzle_size": {"default_value": 0.4, "description": "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size.", "label": "Nozzle Diameter", "maximum_value_warning": "10", "minimum_value": "0.001", "settable_per_extruder": true, "settable_per_mesh": false, "type": "float", "unit": "mm"}}, "description": "Machine specific settings", "label": "Machine", "type": "category"}, "material": {"children": {"material_diameter": {"default_value": 2.85, "description": "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament.", "enabled": "machine_gcode_flavor != \"UltiGCode\"", "label": "Diameter", "maximum_value_warning": "3.5", "minimum_value": "0.0001", "minimum_value_warning": "0.4", "settable_per_extruder": true, "settable_per_mesh": false, "type": "float", "unit": "mm"}}, "description": "Material", "icon": "Spool", "label": "Material", "type": "category"}, "platform_adhesion": {"children": {"extruder_prime_pos_x": {"default_value": 0, "description": "The X coordinate of the position where the nozzle primes at the start of printing.", "enabled": false, "label": "Extruder Prime X Position", "maximum_value": "machine_width", "minimum_value_warning": "machine_nozzle_offset_x", "settable_per_extruder": true, "settable_per_mesh": false, "type": "float", "unit": "mm"}, "extruder_prime_pos_y": {"default_value": 0, "description": "The Y coordinate of the position where the nozzle primes at the start of printing.", "enabled": false, "label": "Extruder Prime Y Position", "maximum_value_warning": "machine_depth", "minimum_value_warning": "machine_nozzle_offset_y", "settable_per_extruder": true, "settable_per_mesh": false, "type": "float", "unit": "mm"}}, "description": "Adhesion", "icon": "Adhesion", "label": "Build Plate Adhesion", "type": "category"}}}