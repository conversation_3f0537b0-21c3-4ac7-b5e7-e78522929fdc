{"version": 2, "name": "ELEGOO Base Printer", "inherits": "fdmprinter", "metadata": {"author": "NARUTO", "manufacturer": "ELEGOO", "file_formats": "text/x-gcode", "platform": "elegoo_platform.3mf", "exclude_materials": ["generic_nylon", "generic_hips", "generic_bvoh", "generic_cpe", "generic_pc", "generic_pva", "generic_pvc"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "elegoo_extruder_0"}, "platform_offset": [-2.2, 14.5, -31], "preferred_material": "generic_pla_175", "preferred_quality_type": "Elegoo_layer_020", "preferred_variant_name": "0.40mm_Elegoo_Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_layer_0": {"value": 500}, "acceleration_print": {"value": 1000}, "acceleration_travel": {"value": "acceleration_print"}, "brim_gap": {"default_value": 0.1}, "brim_width": {"default_value": 5}, "cool_fan_full_at_height": {"value": "layer_height_0 if resolveOrValue('adhesion_type') == 'raft' else resolveOrValue('layer_height_0')+0.4"}, "cool_fan_speed": {"enabled": "resolveOrValue('cool_fan_enabled') == 'true' or cool_fan_enabled"}, "cool_fan_speed_0": {"enabled": "resolveOrValue('cool_fan_enabled') == 'true' or cool_fan_enabled", "value": "cool_fan_speed if resolveOrValue('adhesion_type') == 'raft' else 0"}, "cool_fan_speed_max": {"enabled": "resolveOrValue('cool_fan_enabled') == 'true' or cool_fan_enabled"}, "cool_fan_speed_min": {"enabled": "resolveOrValue('cool_fan_enabled') == 'true' or cool_fan_enabled"}, "fill_outline_gaps": {"default_value": false}, "gantry_height": {"value": "machine_height-20"}, "gradual_support_infill_step_height": {"value": "5"}, "infill_overlap": {"value": "10 if infill_sparse_density < 20.01 else 5 if infill_sparse_density < 40.01 and infill_pattern != 'concentric' else 0"}, "infill_overlap_mm": {"value": "0.5 * (infill_line_width + (wall_line_width_x if wall_line_count > 1 else wall_line_width_0)) * infill_overlap / 100"}, "infill_pattern": {"value": "'lines' if (infill_sparse_density > 35 or speed_print >= 80) else 'grid' "}, "infill_sparse_density": {"default_value": 15}, "initial_layer_line_width_factor": {"value": "100.0 if resolveOrValue('adhesion_type') == 'raft' else 130 if line_width < 0.5 else 110"}, "line_width": {"value": "machine_nozzle_size + 0.02"}, "machine_acceleration": {"value": 1000}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 1000}, "machine_max_acceleration_y": {"value": 1000}, "machine_max_jerk_xy": {"value": 8.0}, "material_diameter": {"value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "material_print_temperature_layer_0": {"value": "210 if material_print_temperature < 210 else material_print_temperature"}, "min_infill_area": {"value": "5"}, "minimum_interface_area": {"default_value": 10}, "minimum_support_area": {"value": "3 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"default_value": true}, "prime_tower_brim_enable": {"value": true}, "prime_tower_min_volume": {"value": "(layer_height) * (prime_tower_size / 2)**2 * 3 * 0.5 "}, "prime_tower_size": {"default_value": 30}, "prime_tower_wipe_enabled": {"default_value": false}, "raft_base_speed": {"value": "0.4 * raft_speed"}, "raft_base_thickness": {"value": "resolveOrValue('machine_nozzle_size') * 0.75"}, "raft_interface_speed": {"value": "0.6 * raft_speed"}, "raft_interface_thickness": {"value": "resolveOrValue('machine_nozzle_size') * 0.75"}, "raft_margin": {"default_value": 5}, "raft_speed": {"value": "30"}, "raft_surface_fan_speed": {"value": "cool_fan_speed"}, "raft_surface_line_width": {"value": "machine_nozzle_size * 1.25"}, "raft_surface_thickness": {"value": "resolveOrValue('machine_nozzle_size')*0.375"}, "retraction_amount": {"default_value": 5}, "retraction_combing": {"value": "'no_outer_surfaces' if (any(extruderValues('skin_monotonic')) or any(extruderValues('ironing_enabled')) or (any(extruderValues('roofing_monotonic')) and any(extruderValues('roofing_layer_count')))) else 'no_outer_surfaces'"}, "retraction_combing_max_distance": {"value": "(infill_line_distance)*1.5 + 5  "}, "retraction_hop": {"value": "layer_height if layer_height > 0.199 else 0.2"}, "skirt_brim_speed": {"value": "speed_print_layer_0"}, "speed_infill": {"value": "math.ceil(speed_wall + 35)"}, "speed_layer_0": {"maximum_value_warning": "60", "value": "speed_print * 20 / 60 if material_bed_temperature > 70 else speed_print * 30 / 60"}, "speed_prime_tower": {"value": "speed_print if speed_print < 70.1 else 90 if speed_print > 100 else speed_print * 0.5+35"}, "speed_slowdown_layers": {"value": "0 if resolveOrValue('adhesion_type') == 'raft' else speed_print / 30"}, "speed_support": {"value": "speed_print if speed_wall > 0 and speed_wall < 40.01 else speed_wall + 40 "}, "speed_topbottom": {"value": "speed_wall + 15"}, "speed_travel": {"value": "90 if speed_print < 60.1 else speed_print * 1.5 if speed_print > 90 else speed_print+30"}, "speed_travel_layer_0": {"value": "70 if speed_layer_0 < 20 else 90 if speed_layer_0 > 40 else speed_layer_0+50"}, "speed_wall_x": {"value": "speed_wall + 15"}, "support_angle": {"value": "45 if speed_print > 99.9 else 50"}, "support_bottom_offset": {"value": "-0.4"}, "support_brim_enable": {"value": "support_structure == 'normal' or support_structure == 'tree'"}, "support_brim_width": {"value": "6 if support_structure == 'tree' else line_width * initial_layer_line_width_factor * 0.02 "}, "support_infill_angles": {"default_value": "[65]"}, "support_interface_density": {"default_value": 33.333}, "support_interface_pattern": {"default_value": "lines"}, "support_material_flow": {"value": "material_flow * 0.95"}, "support_roof_pattern": {"value": "'grid'"}, "support_top_distance": {"value": "extruderValue(support_roof_extruder_nr if support_roof_enable else support_infill_extruder_nr, 'layer_height') + (0 if support_structure == 'tree' else 0)"}, "support_tower_maximum_supported_diameter": {"value": "1"}, "support_xy_distance": {"value": "machine_nozzle_size * 1.7"}, "support_xy_distance_overhang": {"value": "machine_nozzle_size * 1.2 if support_interface_enable else machine_nozzle_size * 0.8"}, "support_z_distance": {"value": "layer_height * 1.2 if (support_interface_enable and support_bottom_enable and layer_height < 0.16) or support_bottom_enable else layer_height"}, "switch_extruder_retraction_amount": {"value": "15 if extruders_enabled_count > 1 else machine_heat_zone_length"}, "wall_thickness": {"value": "wall_line_width_0 if magic_spiralize else line_width * 2"}, "z_seam_corner": {"default_value": "z_seam_corner_weighted"}, "z_seam_position": {"default_value": "left"}, "z_seam_type": {"default_value": "back"}, "zig_zaggify_infill": {"value": "resolveOrValue('infill_pattern') == 'cross' or resolveOrValue('infill_pattern') == 'cross_3d' or resolveOrValue('infill_sparse_density') < 30 "}}}