{"version": 2, "name": "Wanhao Duplicator 5S Mini", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON>", "manufacturer": "Wan<PERSON>", "file_formats": "text/x-gcode", "platform": "wanh<PERSON>_300_200_platform.obj", "has_materials": true, "machine_extruder_trains": {"0": "wanhao_duplicator5Smini_extruder_0"}, "platform_offset": [0, -28, 0], "platform_texture": "Wanhaobackplate.png"}, "overrides": {"machine_depth": {"default_value": 200}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off \n G91 ;relative positioning\n G1 E-1 F300  ;retract the filament a bit before lifting the nozzle, to release some of the pressure\n G1 Z+0.5 E-5 X-20 Y-20 F{speed_travel} ;move Z up a bit and retract filament even more\n G28 X0 Y0 ;move X/Y to min endstops, so the head is out of the way\n M84 ;steppers off\n G90 ;absolute positioning"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": false}, "machine_height": {"default_value": 180}, "machine_name": {"default_value": "Wanhao Duplicator 5S Mini"}, "machine_start_gcode": {"default_value": "G21 ;metric values\n G90 ;absolute positioning\n M82 ;set extruder to absolute mode\n M107 ;start with the fan off\n G28 X0 Y0 ;move X/Y to min endstops\n G28 Z0 ;move Z to min endstops\n G1 Z15.0 F{speed_travel} ;move the platform down 15mm\n G92 E0 ;zero the extruded length\n G1 F200 E6 ;extrude 6 mm of feed stock\n G92 E0 ;zero the extruded length again\n G1 F{speed_travel} \n ;Put printing message on LCD screen\n M117 Printing..."}, "machine_width": {"default_value": 300}}}