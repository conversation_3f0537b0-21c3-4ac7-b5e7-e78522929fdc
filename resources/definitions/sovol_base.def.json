{"version": 2, "name": "Sovol Base Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "ed3d.net", "manufacturer": "Sovol 3D", "file_formats": "text/x-gcode", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "innofill_innoflex60", "verbatim_bvoh"], "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "preferred_quality_type": "standard", "preferred_variant_name": "0.4mm Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_print": {"value": "machine_acceleration"}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": 500}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "infill_before_walls": {"value": false}, "infill_line_distance": {"value": "0 if infill_sparse_density == 0 else (infill_line_width * 100) / infill_sparse_density * (2 if infill_pattern == 'grid' else (3 if infill_pattern == 'triangles' or infill_pattern == 'trihexagon' or infill_pattern == 'cubic' or infill_pattern == 'cubicsubdiv' else (2 if infill_pattern == 'tetrahedral' or infill_pattern == 'quarter_cubic' else (1 if infill_pattern == 'cross' or infill_pattern == 'cross_3d' else 1))))"}, "infill_overlap": {"value": 30.0}, "infill_pattern": {"value": "'zigzag' if infill_sparse_density > 89 else 'cubic'"}, "infill_sparse_density": {"value": 10.0}, "infill_wipe_dist": {"value": 0.0}, "initial_layer_line_width_factor": {"value": 150}, "jerk_enabled": {"value": true}, "jerk_print": {"value": 8}, "jerk_travel": {"value": "jerk_print * 2"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 500}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 500}, "machine_max_acceleration_y": {"value": 500}, "machine_max_feedrate_e": {"value": 50}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 10}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 8}, "machine_max_jerk_z": {"value": 0.4}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "optimize_wall_printing_order": {"value": true}, "retraction_amount": {"default_value": 0.5}, "retraction_speed": {"default_value": 40}, "skin_monotonic": {"value": true}, "skin_overlap": {"value": 10.0}, "speed_layer_0": {"value": 20.0}, "speed_print": {"value": 50.0}, "speed_topbottom": {"value": "math.ceil(speed_print * (50 / 100))"}, "speed_wall": {"value": "math.ceil(speed_print * (50 / 100))"}, "wall_0_wipe_dist": {"value": 0.0}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_corner": {"value": "'z_seam_corner_inner'"}, "z_seam_type": {"value": "'sharpest_corner'"}, "zig_zaggify_infill": {"value": true}}}