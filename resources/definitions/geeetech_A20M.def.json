{"version": 2, "name": "Geeetech A20M", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Amit L", "manufacturer": "<PERSON><PERSON><PERSON>", "file_formats": "text/x-gcode", "has_materials": true, "machine_extruder_trains": {"0": "geeetech_A20M_1", "1": "geeetech_A20M_2"}}, "overrides": {"adhesion_type": {"default_value": "skirt"}, "gantry_height": {"value": "28"}, "layer_height": {"default_value": 0.1}, "layer_height_0": {"default_value": 0.15}, "machine_acceleration": {"default_value": 1000}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 250}, "machine_end_gcode": {"default_value": "G91\nG1 E-1\nG0 X0 Y200\nM104 S0\nG90\nG92 E0\nM140 S0\nM84\nM104 S0\nM140 S0\nM84"}, "machine_extruder_count": {"default_value": 2}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-31, 31], [34, 31], [34, -40], [-31, -40]]}, "machine_height": {"default_value": 250}, "machine_max_acceleration_z": {"default_value": 500}, "machine_max_feedrate_e": {"default_value": 120}, "machine_max_feedrate_z": {"default_value": 12}, "machine_max_jerk_e": {"default_value": 2.5}, "machine_max_jerk_xy": {"default_value": 10}, "machine_max_jerk_z": {"default_value": 0.2}, "machine_name": {"default_value": "Geeetech A20M"}, "machine_start_gcode": {"default_value": ";GeeeTech A20M start script\nG28 ;home\nG90 ;absolute positioning\nG1 X0 Y0 Z15 E0 F300 ;go to wait position\nM140 S{material_bed_temperature_layer_0} ;set bed temp\nM109 S{material_print_temperature_layer_0} ;set extruder temp and wait\nG1 Z0.8 F200 ;set extruder height\nG1 X220 Y0 E80 F1000 ;purge line\n;end of start script"}, "machine_width": {"default_value": 250}, "retraction_amount": {"default_value": 0.8}, "retraction_speed": {"default_value": 35}}}