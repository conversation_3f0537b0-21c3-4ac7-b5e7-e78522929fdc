{"version": 2, "name": "Tizyx K25", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Tizyx", "manufacturer": "Tizyx", "file_formats": "text/x-gcode", "platform": "tizyx_k25_platform.3mf", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "generic_abs", "generic_cpe", "generic_hips", "generic_nylon", "generic_pc", "generic_petg", "generic_pla", "generic_pva", "generic_tpu", "innofill_innoflex60", "verbatim_bvoh"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "tizyx_k25_extruder_0"}, "platform_offset": [0, -4, 0], "preferred_material": "tizyx_pla", "preferred_variant_name": "0.4 mm"}, "overrides": {"acceleration_enabled": {"value": "False"}, "acceleration_print": {"value": "1500"}, "gantry_height": {"value": "500"}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 255}, "machine_end_gcode": {"default_value": "M104 S0\nM140 S0\nG91\nG1 E-5 F300\nG1 Z+3 F3000\nG1 Y245 F3000\nM84"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[25, 49], [25, -49], [-25, -49], [-25, 49]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 255}, "machine_name": {"default_value": "TiZYX K25"}, "machine_start_gcode": {"default_value": "M82\nG90\nG28 X\nG28 Y\nG28 Z\nG29\nG91\nG1 Z0\nG90\nM82\nG92 E0\nG1 X125 Y245 F3000\nG1 Z0"}, "machine_width": {"default_value": 255}, "retraction_combing": {"value": "'off'"}, "z_seam_type": {"default_value": "back"}, "z_seam_x": {"value": "127.5"}, "z_seam_y": {"value": "250"}}}