{"version": 2, "name": "Geeetech MeCreator2", "inherits": "Geeetech_Base_Single_Extruder", "metadata": {"visible": true, "machine_extruder_trains": {"0": "Geeetech_Single_Extruder"}}, "overrides": {"gantry_height": {"value": 45}, "machine_depth": {"default_value": 160}, "machine_end_gcode": {"default_value": "G91 ;Switch to relative positioning\nG1 E-1.5 F2700 ;Retract filament\nG1 E-1.5 Z0.2 F2400 ;Retract and raise Z\nG1 X5 Y5 F3000 ;Move away\nG1 Z10 ;lift print head\nG90 ;Switch to absolute positioning\nG28 X Y ;homing XY\nM106 S0 ;off Fan\nM104 S0 ;Cooldown hotend\nM140 S0 ;Cooldown bed\nM84 X Y E ;Disable steppers"}, "machine_head_with_fans_polygon": {"default_value": [[-75, 35], [18, 35], [18, -18], [-75, -18]]}, "machine_height": {"default_value": 160}, "machine_name": {"default_value": "Geeetech MeCreator2"}, "machine_start_gcode": {"default_value": ";Geeetech MeCreator2 Custom Start G-code\nM104 S{material_print_temperature_layer_0} ; Set Hotend Temperature\nM190 S{material_bed_temperature_layer_0} ; Wait for Bed Temperature\nM109 S{material_print_temperature_layer_0} ; Wait for Hotend Temperature\nG92 E0 ; Reset Extruder\nG28 ; Home all axes\nM107 P0 ;Off Main Fan\nM107 P1 ;Off Aux Fan\nM2012 P8 S1 F100 ; ON Light\nG1 Z5.0 F3000 ;Move Z Axis up little to prevent scratching of Heat Bed\nG1 X0.1 Y10 Z0.8 F5000 ; Move to start position\nG1 X0.1 Y140.0 Z1.2 F1500 E30 ; Draw the first line\nG92 E0 ; Reset Extruder\nG1 X0.4 Y140.0 Z1.2 F3000 ; Move to side a little\nG1 X0.4 Y10 Z1.2 F1500 E25 ; Draw the second line\nG92 E0 ; Reset Extruder\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X5 Y10 Z0.4 F3000.0 ; Scrape off nozzle residue"}, "machine_width": {"default_value": 160}, "retraction_amount": {"value": 2}}}