{"version": 2, "name": "Kossel Pro", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON>", "manufacturer": "<PERSON>", "file_formats": "text/x-gcode", "platform": "kossel_pro_build_platform.3mf", "machine_extruder_trains": {"0": "kossel_pro_extruder_0"}, "platform_offset": [0, -0.25, 0]}, "overrides": {"machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 240}, "machine_end_gcode": {"default_value": "M104 S0 ; turn off temperature\nM140 S0 ; turn off bed\nG28 ; home all axes\nM84 ; disable motors\n"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 240}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": "; info: M303 E0 S200 C8 ; Pid auto-tune \n\nM140 S{material_bed_temperature}; Start heating up the base\nG28 ; Home to top 3 endstops\n; Autolevel and adjust first layer\n; Adjust this value to fit your own printer!  (positive is thicker)\n; This default value is intentionally very high to accommodate the\n; variety of print heads used with this printer.  Many of you will\n; need tiny values like Z0 or Z0.1.  Use feeler gauges to dial this\n; in as accurately as possible.\nG29 Z10\n\n; Squirt and wipe ;\nM109 S220 ; Wait for the temp to hit 220\nG00 X125 Y-60 Z0.1 ;\nG92 E0 ;\nG01 E25 F100 ; Extrude a little bit to replace oozage from auto levelling\nG01 X90 Y-50 F6000 ;\nG01 Z5 ;\n\n; Set the extruder to the requested print temperature\nM104 S{material_print_temperature}\n"}, "machine_width": {"default_value": 240}}}