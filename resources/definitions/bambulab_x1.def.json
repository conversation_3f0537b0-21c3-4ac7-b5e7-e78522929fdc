{"version": 2, "name": "BambuLab X1", "inherits": "bambulab_base", "metadata": {"visible": true, "platform": "bambulab_x1.obj", "has_machine_quality": true, "has_material": true, "has_textured_buildplate": true, "has_variant_buildplates": false, "has_variants": true, "machine_extruder_trains": {"0": "bambulab_x1_extruder_0", "1": "bambulab_x1_extruder_1", "2": "bambulab_x1_extruder_2", "3": "bambulab_x1_extruder_3"}, "platform_offset": [-130, 0, 130], "platform_texture": "bambulab-buildplate.png", "preferred_variant_name": "X1 0.4mm", "weight": 3}, "overrides": {"machine_depth": {"value": 256}, "machine_disallowed_areas": {"default_value": [[[-128, 100], [-110, 100], [-110, 128], [-128, 128]]]}, "machine_end_gcode": {"default_value": ";===== date: 20240528 =====================\nM400 ; wait for buffer to clear\nG92 E0 ; zero the extruder\nG1 E-0.8 F1800 ; retract\nG1 Z{machine_height + 0.5} F900 ; lower z a little\nG1 X65 Y245 F12000 ; move to safe pos\nG1 Y265 F3000\n\nG1 X65 Y245 F12000\nG1 Y265 F3000\nM140 S0 ; turn off bed\nM106 S0 ; turn off fan\nM106 P2 S0 ; turn off remote part cooling fan\nM106 P3 S0 ; turn off chamber cooling fan\n\nG1 X100 F12000 ; wipe\n; pull back filament to AMS\nM620 S255\nG1 X20 Y50 F12000\nG1 Y-3\nT255\nG1 X65 F12000\nG1 Y265\nG1 X100 F12000 ; wipe\nM621 S255\nM104 S0 ; turn off hotend\n\nM622.1 S1 ; for prev firware, default turned on\nM1002 judge_flag timelapse_record_flag\nM622 J1\n    M400 ; wait all motion done\n    M991 S0 P-1 ;end smooth timelapse at safe pos\n    M400 S3 ;wait for last picture to be taken\nM623; end of 'timelapse_record_flag'\n\nM400 ; wait all motion done\nM17 S\nM17 Z0.4 ; lower z motor current to reduce impact if there is something in the bottom\nG1 Z250 F600\nG1 Z248\nM400 P100\nM17 R ; restore z current\n\nM220 S100  ; Reset feedrate magnitude\nM201.2 K1.0 ; Reset acc magnitude\nM73.2   R1.0 ;Reset left time magnitude\nM1002 set_gcode_claim_speed_level : 0\n\nM17 X0.8 Y0.8 Z0.5 ; lower motor current to 45% power\nM960 S5 P0 ; turn off logo lamp\n"}, "machine_extruder_count": {"value": 4}, "machine_height": {"value": 251}, "machine_name": {"default_value": "BambuLab Bambu X1"}, "machine_scan_first_layer": {"enabled": true, "value": "machine_buildplate_type!='textured_pei_plate'"}, "machine_start_gcode": {"default_value": ";===== machine: X1 ====================\n;===== date: 20241023 ==================\n;===== turn on the HB fan =================\nM104 S75 ;set extruder temp to turn on the HB fan and prevent filament oozing from nozzle\n;===== reset machine status =================\nM290 X40 Y40 Z2.6666666\nG91\nM17 Z0.4 ; lower the z-motor current\nG380 S2 Z30 F300 ; G380 is same as G38; lower the hotbed , to prevent the nozzle is below the hotbed\nG380 S2 Z-25 F300 ;\nG1 Z5 F300;\nG90\nM17 X1.2 Y1.2 Z0.75 ; reset motor current to default\nM960 S5 P1 ; turn on logo lamp\nG90\nM220 S100 ;Reset Feedrate\nM221 S100 ;Reset Flowrate\nM73.2   R1.0 ;Reset left time magnitude\nM1002 set_gcode_claim_speed_level : 5\nM221 X0 Y0 Z0 ; turn off soft endstop to prevent protential logic problem\nG29.1 Z0 ; clear z-trim value first\nM204 S10000 ; init ACC set to 10m/s^2\n\n;===== heatbed preheat ====================\nM1002 gcode_claim_action : 2\nM140 S{material_bed_temperature_layer_0} ;set bed temp\nM190 S{material_bed_temperature_layer_0} ;wait for bed temp\n\n{if machine_scan_first_layer}\n;=========register first layer scan=====\nM977 S1 P60\n{endif}\n\n;=============turn on fans to prevent PLA jamming=================\n{if material_type=='PLA' and (material_bed_temperature_layer_0>45 or material_bed_temperature>45), initial_extruder_nr}\n    M106 P3 S180\n    M142 P1 R35 S40\n{endif}\n{if material_type=='PLA' and (material_bed_temperature_layer_0<=45 and material_bed_temperature<=45) , initial_extruder_nr}\n    M142 P1 R35 S40\n{endif}\nM106 P2 S100 ; turn on big fan ,to cool down toolhead\n\n;===== prepare print temperature and material ==========\nM104 S{material_print_temperature_layer_0, initial_extruder_nr} ;set extruder temp\nG91\nG0 Z10 F1200\nG90\nG28 X\nM975 S1 ; turn on\nG1 X60 F12000\nG1 Y245\nG1 Y265 F3000\nM620 M\nM620 S{initial_extruder_nr}A   ; switch material if AMS exist\n    M109 S{material_print_temperature_layer_0, initial_extruder_nr}\n    G1 X120 F12000\n\n    G1 X20 Y50 F12000\n    G1 Y-3\n    T{initial_extruder_nr}\n    G1 X54 F12000\n    G1 Y265\n    M400\nM621 S{initial_extruder_nr}A\nM620.1 E F{material_max_flowrate/2.4053*60, initial_extruder_nr} T{material_print_temperature, initial_extruder_nr}\n\n\nM412 S1 ; ===turn on filament runout detection===\n\nM109 S250 ;set nozzle to common flush temp\nM106 P1 S0\nG92 E0\nG1 E50 F200\nM400\nM104 S{material_print_temperature_layer_0, initial_extruder_nr}\nG92 E0\nG1 E50 F200\nM400\nM106 P1 S255\nG92 E0\nG1 E5 F300\nM109 S{material_print_temperature_layer_0-20, initial_extruder_nr} ; drop nozzle temp, make filament shink a bit\nG92 E0\nG1 E-0.5 F300\n\nG1 X70 F9000\nG1 X76 F15000\nG1 X65 F15000\nG1 X76 F15000\nG1 X65 F15000; shake to put down garbage\nG1 X80 F6000\nG1 X95 F15000\nG1 X80 F15000\nG1 X165 F15000; wipe and shake\nM400\nM106 P1 S0\n;===== prepare print temperature and material end =====\n\n\n;===== wipe nozzle ===============================\nM1002 gcode_claim_action : 14\nM975 S1\nM106 S255\nG1 X65 Y230 F18000\nG1 Y264 F6000\nM109 S{material_print_temperature_layer_0-20, initial_extruder_nr}\nG1 X100 F18000 ; first wipe mouth\n\nG0 X135 Y253 F20000  ; move to exposed steel surface edge\nG28 Z P0 T300; home z with low precision,permit 300deg temperature\nG29.2 S0 ; turn off ABL\nG0 Z5 F20000\n\nG1 X60 Y265\nG92 E0\nG1 E-0.5 F300 ; retrack more\nG1 X100 F5000; second wipe mouth\nG1 X70 F15000\nG1 X100 F5000\nG1 X70 F15000\nG1 X100 F5000\nG1 X70 F15000\nG1 X100 F5000\nG1 X70 F15000\nG1 X90 F5000\nG0 X128 Y261 Z-1.5 F20000  ; move to exposed steel surface and stop the nozzle\nM104 S140 ; set temp down to heatbed acceptable\nM106 S255 ; turn on fan (G28 has turn off fan)\n\nM221 S; push soft endstop status\nM221 Z0 ;turn off Z axis endstop\nG0 Z0.5 F20000\nG0 X125 Y259.5 Z-1.01\nG0 X131 F211\nG0 X124\nG0 Z0.5 F20000\nG0 X125 Y262.5\nG0 Z-1.01\nG0 X131 F211\nG0 X124\nG0 Z0.5 F20000\nG0 X125 Y260.0\nG0 Z-1.01\nG0 X131 F211\nG0 X124\nG0 Z0.5 F20000\nG0 X125 Y262.0\nG0 Z-1.01\nG0 X131 F211\nG0 X124\nG0 Z0.5 F20000\nG0 X125 Y260.5\nG0 Z-1.01\nG0 X131 F211\nG0 X124\nG0 Z0.5 F20000\nG0 X125 Y261.5\nG0 Z-1.01\nG0 X131 F211\nG0 X124\nG0 Z0.5 F20000\nG0 X125 Y261.0\nG0 Z-1.01\nG0 X131 F211\nG0 X124\nG0 X128\nG2 I0.5 J0 F300\nG2 I0.5 J0 F300\nG2 I0.5 J0 F300\nG2 I0.5 J0 F300\n\nM109 S140 ; wait nozzle temp down to heatbed acceptable\nG2 I0.5 J0 F3000\nG2 I0.5 J0 F3000\nG2 I0.5 J0 F3000\nG2 I0.5 J0 F3000\n\nM221 R; pop softend status\nG1 Z10 F1200\nM400\nG1 Z10\nG1 F30000\nG1 X128 Y128\nG29.2 S1 ; turn on ABL\n;G28 ; home again after hard wipe mouth\nM106 S0 ; turn off fan , too noisy\n;===== wipe nozzle end ================================\n\n;===== check scanner clarity ===========================\nG1 X128 Y128 F24000\nG28 Z P0\nM972 S5 P0\nG1 X230 Y15 F24000\n;===== check scanner clarity end =======================\n\n;===== bed leveling ==================================\nM1002 judge_flag g29_before_print_flag\nM622 J1\n\n    M1002 gcode_claim_action : 1\n    G29 A X{-machine_width/2 if machine_center_is_zero else 0} Y{-machine_depth/2 if machine_center_is_zero else 0} I{machine_width} J{machine_depth}\n    M400\n    M500 ; save cali data\n\nM623\n;===== bed leveling end ================================\n\n;===== home after wipe mouth============================\nM1002 judge_flag g29_before_print_flag\nM622 J0\n\n    M1002 gcode_claim_action : 13\n    G28\n\nM623\n;===== home after wipe mouth end =======================\n\nM975 S1 ; turn on vibration supression\n\n;=============turn on fans to prevent PLA jamming=================\n{if material_type=='PLA' and (material_bed_temperature_layer_0>45 or material_bed_temperature>45), initial_extruder_nr}\n    M106 P3 S180\n    M142 P1 R35 S40\n{endif}\n{if material_type=='PLA' and (material_bed_temperature_layer_0<=45 and material_bed_temperature<=45) , initial_extruder_nr}\n    M142 P1 R35 S40\n{endif}\nM106 P2 S100 ; turn on big fan ,to cool down toolhead\n\nM104 S{material_print_temperature_layer_0, initial_extruder_nr} ; set extrude temp earlier, to reduce wait time\n\n;===== mech mode fast check============================\nG1 X128 Y128 Z10 F20000\nM400 P200\nM970.3 Q1 A7 B30 C80  H15 K0\nM974 Q1 S2 P0\n\nG1 X128 Y128 Z10 F20000\nM400 P200\nM970.3 Q0 A7 B30 C90 Q0 H15 K0\nM974 Q0 S2 P0\n\nM975 S1\nG1 F30000\nG1 X230 Y15\nG28 X ; re-home XY\n;===== mech mode fast check============================\n\n{if machine_scan_first_layer}\n;start heatbed  scan====================================\nM976 S2 P1\nG90\nG1 X128 Y128 F20000\nM976 S3 P2  ;register void printing detection\n{endif}\n\n;===== nozzle load line ===============================\nM975 S1\nG90\nM83\nT1000\nG1 X18.0 Y1.0 Z0.8 F18000;Move to start position\nM109 S{material_print_temperature, initial_extruder_nr}\nG1 Z0.2\nG0 E2 F300\nG0 X240 E15 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)      * 60, initial_extruder_nr}\nG0 Y11 E0.700 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\nG0 X239.5\nG0 E0.2\nG0 Y1.5 E0.700\nG0 X231 E0.700 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)   * 60, initial_extruder_nr}\nM400\n\n;===== for Textured PEI Plate , lower the nozzle as the nozzle was touching topmost of the texture when homing ==\n;curr_bed_type={curr_bed_type}\n{if machine_buildplate_type=='textured_pei_plate'}\nG29.1 Z{-0.04} ; for Textured PEI Plate\n{endif}\n\n;===== draw extrinsic para cali paint =================\nM1002 judge_flag extrude_cali_flag\nM622 J1\n\n    M1002 gcode_claim_action : 8\n\n    T1000\n\n    G0 F1200.0 X231 Y15   Z0.2 E0.741\n    G0 F1200.0 X226 Y15   Z0.2 E0.275\n    G0 F1200.0 X226 Y8    Z0.2 E0.384\n    G0 F1200.0 X216 Y8    Z0.2 E0.549\n    G0 F1200.0 X216 Y1.5  Z0.2 E0.357\n\n    G0 X48.0 E12.0 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5) * 60, initial_extruder_nr}\n    G0 X48.0 Y14 E0.92 F1200.0\n    G0 X35.0 Y6.0 E1.03 F1200.0\n\n    ;=========== extruder cali extrusion ==================\n    T1000\n    M83\n    {if acceleration_print > 0 and acceleration_wall_0 > 0, initial_extruder_nr}\n        M204 S{acceleration_wall_0, initial_extruder_nr}\n    {endif}\n    {if acceleration_print > 0 and acceleration_wall_0 <= 0, initial_extruder_nr}\n        M204 S{acceleration_print, initial_extruder_nr}\n    {endif}\n\n    G0 X35.000 Y6.000 Z0.300 F30000 E0\n    G1 F1500.000 E0.800\n    M106 S0 ; turn off fan\n    G0 X185.000 E9.35441 F4800\n    G0 X187 Z0\n    G1 F1500.000 E-0.800\n    G0 Z1\n    G0 X180 Z0.3 F18000\n\n    M900 L1000.0 M1.0\n    M900 K0.160\n    G0 X45.000 F30000\n    G0 Y8.000 F30000\n    G1 F1500.000 E0.800\n    G1 X65.000 E1.24726 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X70.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X75.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X80.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X85.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X90.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X95.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X100.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X105.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X110.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X115.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X120.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X125.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X130.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X135.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X140.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X145.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X150.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X155.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X160.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X165.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X170.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X175.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X180.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 F1500.000 E-0.800\n    G1 X183 Z0.15 F30000\n    G1 X185\n    G1 Z1.0\n    G0 Y6.000 F30000 ; move y to clear pos\n    G1 Z0.3\n    M400\n\n    G0 X45.000 F30000\n    M900 K0.080\n    G0 X45.000 F30000\n    G0 Y10.000 F30000\n    G1 F1500.000 E0.800\n    G1 X65.000 E1.24726 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X70.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X75.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X80.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X85.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X90.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X95.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X100.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X105.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X110.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X115.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X120.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X125.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X130.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X135.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X140.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X145.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X150.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X155.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X160.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X165.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X170.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X175.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X180.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 F1500.000 E-0.800\n    G1 X183 Z0.15 F30000\n    G1 X185\n    G1 Z1.0\n    G0 Y6.000 F30000 ; move y to clear pos\n    G1 Z0.3\n    M400\n\n    G0 X45.000 F30000\n    M900 K0.000\n    G0 X45.000 F30000\n    G0 Y12.000 F30000\n    G1 F1500.000 E0.800\n    G1 X65.000 E1.24726 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X70.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X75.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X80.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X85.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X90.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X95.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X100.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X105.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X110.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X115.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X120.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X125.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X130.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X135.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X140.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X145.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X150.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X155.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X160.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X165.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X170.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X175.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X180.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 F1500.000 E-0.800\n    G1 X183 Z0.15 F30000\n    G1 X185\n    G1 Z1.0\n    G0 Y6.000 F30000 ; move y to clear pos\n    G1 Z0.3\n\n    G0 X45.000 F30000 ; move to start point\n\nM623 ; end of 'draw extrinsic para cali paint'\n\nM1002 judge_flag extrude_cali_flag\nM622 J0\n    G0 X231 Y1.5 F30000\n    G0 X18 E14.3 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5) * 60, initial_extruder_nr}\nM623\n\nM104 S140\n\n\n;=========== laser and rgb calibration ===========\nM400\nM18 E\nM500 R\n\nM973 S3 P14\n\nG1 X120 Y1.0 Z0.3 F18000.0;Move to first extrude line pos\nT1100\nG1 X235.0 Y1.0 Z0.3 F18000.0;Move to first extrude line pos\nM400 P100\nM960 S1 P1\nM400 P100\nM973 S6 P0; use auto exposure for horizontal laser by xcam\nM960 S0 P0\n\nG1 X240.0 Y6.0 Z0.3 F18000.0;Move to vertical extrude line pos\nM960 S2 P1\nM400 P100\nM973 S6 P1; use auto exposure for vertical laser by xcam\nM960 S0 P0\n\n;=========== handeye calibration ======================\nM1002 judge_flag extrude_cali_flag\nM622 J1\n\n    M973 S3 P1 ; camera start stream\n    M400 P500\n    M973 S1\n    G0 F6000 X228.500 Y4.500 Z0.000\n    M960 S0 P1\n    M973 S1\n    M400 P800\n    M971 S6 P0\n    M973 S2 P0\n    M400 P500\n    G0 Z0.000 F12000\n    M960 S0 P0\n    M960 S1 P1\n    G0 X221.00 Y4.50\n    M400 P200\n    M971 S5 P1\n    M973 S2 P1\n    M400 P500\n    M960 S0 P0\n    M960 S2 P1\n    G0 X228.5 Y11.0\n    M400 P200\n    M971 S5 P3\n    G0 Z0.500 F12000\n    M960 S0 P0\n    M960 S2 P1\n    G0 X228.5 Y11.0\n    M400 P200\n    M971 S5 P4\n    M973 S2 P0\n    M400 P500\n    M960 S0 P0\n    M960 S1 P1\n    G0 X221.00 Y4.50\n    M400 P500\n    M971 S5 P2\n    M963 S1\n    M400 P1500\n    M964\n    T1100\n    G0 F6000 X228.500 Y4.500 Z0.000\n    M960 S0 P1\n    M973 S1\n    M400 P800\n    M971 S6 P0\n    M973 S2 P0\n    M400 P500\n    G0 Z0.000 F12000\n    M960 S0 P0\n    M960 S1 P1\n    G0 X221.00 Y4.50\n    M400 P200\n    M971 S5 P1\n    M973 S2 P1\n    M400 P500\n    M960 S0 P0\n    M960 S2 P1\n    G0 X228.5 Y11.0\n    M400 P200\n    M971 S5 P3\n    G0 Z0.500 F12000\n    M960 S0 P0\n    M960 S2 P1\n    G0 X228.5 Y11.0\n    M400 P200\n    M971 S5 P4\n    M973 S2 P0\n    M400 P500\n    M960 S0 P0\n    M960 S1 P1\n    G0 X221.00 Y4.50\n    M400 P500\n    M971 S5 P2\n    M963 S1\n    M400 P1500\n    M964\n    T1100\n    G1 Z3 F3000\n\n    M400\n    M500 ; save cali data\n\n    M104 S{material_print_temperature, initial_extruder_nr} ; rise nozzle temp now ,to reduce temp waiting time.\n\n    T1100\n    M400 P400\n    M960 S0 P0\n    G0 F30000.000 Y10.000 X65.000 Z0.000\n    M400 P400\n    M960 S1 P1\n    M400 P50\n\n    M969 S1 N3 A2000\n    G0 F360.000 X181.000 Z0.000\n    M980.3 A70.000 B{speed_wall_0*wall_line_width_0*layer_height/(1.75*1.75/4*3.14)*60/4, initial_extruder_nr} C5.000 D{speed_wall_0*wall_line_width_0*layer_height/(1.75*1.75/4*3.14)*60, initial_extruder_nr} E5.000 F175.000 H1.000 I0.000 J0.080 K0.160\n    M400 P100\n    G0 F20000\n    G0 Z1 ; rise nozzle up\n    T1000 ; change to nozzle space\n    G0 X45.000 Y4.000 F30000 ; move to test line pos\n    M969 S0 ; turn off scanning\n    M960 S0 P0\n\n\n    G1 Z2 F20000\n    T1000\n    G0 X45.000 Y4.000 F30000 E0\n    M109 S{material_print_temperature, initial_extruder_nr}\n    G0 Z0.3\n    G1 F1500.000 E3.600\n    G1 X65.000 E1.24726 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X70.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X75.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X80.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X85.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X90.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X95.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X100.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X105.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X110.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X115.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X120.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X125.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X130.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X135.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n\n    ; see if extrude cali success, if not ,use default value\n    M1002 judge_last_extrude_cali_success\n    M622 J0\n        M400\n        M900 K0.08 M{speed_wall_0*wall_line_width_0*layer_height/(1.75*1.75/4*3.14)*0.08, initial_extruder_nr}\n    M623\n\n    G1 X140.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X145.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X150.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X155.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X160.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X165.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X170.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X175.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X180.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X185.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X190.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X195.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X200.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X205.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X210.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X215.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    G1 X220.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/ 4 * 60, initial_extruder_nr}\n    G1 X225.000 E0.31181 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)    * 60, initial_extruder_nr}\n    M973 S4\n\nM623\n\n;========turn off light and wait extrude temperature =============\nM1002 gcode_claim_action : 0\nM973 S4 ; turn off scanner\nM400 ; wait all motion done before implement the emprical L parameters\n;M900 L500.0 ; Empirical parameters\nM109 S{material_print_temperature_layer_0, initial_extruder_nr}\nM960 S1 P0 ; turn off laser\nM960 S2 P0 ; turn off laser\nM106 S0 ; turn off fan\nM106 P2 S0 ; turn off big fan\nM106 P3 S0 ; turn off chamber fan\n\nM975 S1 ; turn on mech mode supression\nG90\nM83\nT1000\n;===== purge line to wipe the nozzle ============================\nG1 E{-retraction_amount, initial_extruder_nr} F1800\nG1 X18.0 Y2.5 Z0.8 F18000.0;Move to start position\nG1 E{retraction_amount, initial_extruder_nr} F1800\nM109 S{material_print_temperature_layer_0, initial_extruder_nr}\nG1 Z0.2\nG0 X239 E15 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)  * 60, initial_extruder_nr}\nG0 Y12 E0.7 F{speed_wall_0*wall_line_width_0*layer_height/(0.3*0.5)/4* 60, initial_extruder_nr}\n"}, "machine_width": {"value": 256}, "prime_tower_position_x": {"value": "resolveOrValue('prime_tower_size') + (resolveOrValue('prime_tower_base_size') if (resolveOrValue('adhesion_type') == 'raft' or resolveOrValue('prime_tower_brim_enable')) else 0) + max(max(extruderValues('travel_avoid_distance')) + max(extruderValues('machine_nozzle_offset_y')) + max(extruderValues('support_offset')) + (extruderValue(skirt_brim_extruder_nr, 'skirt_brim_line_width') * extruderValue(skirt_brim_extruder_nr, 'skirt_line_count') * extruderValue(skirt_brim_extruder_nr, 'initial_layer_line_width_factor') / 100 + extruderValue(skirt_brim_extruder_nr, 'skirt_gap') if resolveOrValue('adhesion_type') == 'skirt' else 0) + (resolveOrValue('draft_shield_dist') if resolveOrValue('draft_shield_enabled') else 0), max(map(abs, extruderValues('machine_nozzle_offset_y'))), 1) - (resolveOrValue('machine_depth') / 2 if resolveOrValue('machine_center_is_zero') else 0)"}, "travel_avoid_distance": {"value": "3"}}}