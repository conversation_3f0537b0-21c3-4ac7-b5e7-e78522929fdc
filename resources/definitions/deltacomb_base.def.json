{"version": 2, "name": "Deltacomb Base Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "<PERSON><PERSON>", "manufacturer": "Deltacomb 3D Printers", "file_formats": "text/x-gcode", "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "deltacomb_base_extruder_0", "1": "deltacomb_base_extruder_1", "2": "deltacomb_base_extruder_2", "3": "deltacomb_base_extruder_3"}, "preferred_material": "generic_pla", "preferred_quality_type": "D020", "variants_name": "Head"}, "overrides": {"acceleration_enabled": {"value": "True"}, "acceleration_layer_0": {"value": "acceleration_wall_0"}, "acceleration_prime_tower": {"value": "acceleration_wall"}, "acceleration_print": {"value": "4000"}, "acceleration_support": {"value": "acceleration_wall"}, "acceleration_support_interface": {"value": "acceleration_wall_0"}, "acceleration_topbottom": {"value": "acceleration_wall_0"}, "acceleration_travel": {"value": "9000"}, "acceleration_wall": {"value": "acceleration_print * 0.5"}, "acceleration_wall_0": {"value": "acceleration_wall * 0.5"}, "brim_width": {"value": "3"}, "infill_before_walls": {"default_value": false}, "infill_pattern": {"value": "'cubic'"}, "infill_sparse_density": {"default_value": 30}, "jerk_enabled": {"value": "True"}, "jerk_infill": {"value": "10"}, "jerk_print": {"value": "25"}, "jerk_travel": {"value": "10"}, "machine_center_is_zero": {"default_value": true}, "machine_end_gcode": {"default_value": ";---------------------------------------\n;Deltacomb end script\n;---------------------------------------\nG91 ;relative positioning\nG1 F15000 X8.0 E-4.5 ;Wipe filament+material retraction\nG1 F15000 E4.0 Z1 ;Retraction compensation\nG28 ;Home all axes (max endstops)\nM84 ;steppers off\n"}, "machine_extruder_count": {"default_value": 1, "maximum_value": "4"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_max_feedrate_z": {"default_value": 300}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": ";---------------------------------------\n;<PERSON><PERSON> start script\n;---------------------------------------\nG21 ;metric values\nG90 ;absolute positioning\nM107 ;start with the fan off\nG28 ;Home all axes (max endstops)\nM420 S1; Bed Level Enable\nG92 E0 ;zero the extruded length\nG1 Z15.0 F9000 ;move to the platform down 15mm\nG1 F9000\n\n;Put printing message on LCD screen\nM117 In stampa...\nM140 S{print_bed_temperature} ;set the target bed temperature\n;---------------------------------------"}, "prime_tower_brim_enable": {"value": false}, "prime_tower_size": {"value": "math.sqrt(extruders_enabled_count * prime_tower_min_volume / layer_height / math.pi) * 2"}, "retraction_amount": {"default_value": 3.5}, "retraction_combing": {"value": "'noskin'"}, "retraction_hop": {"default_value": 1.0}, "retraction_hop_enabled": {"default_value": true}, "retraction_hop_only_when_collides": {"value": "1"}, "retraction_speed": {"default_value": 40}, "roofing_layer_count": {"value": "1"}, "roofing_line_width": {"value": "line_width * 0.75"}, "skirt_brim_minimal_length": {"default_value": 150}, "skirt_brim_speed": {"value": "speed_layer_0"}, "skirt_line_count": {"default_value": 3}, "speed_layer_0": {"value": "speed_print * 0.55"}, "speed_print": {"default_value": 80}, "speed_topbottom": {"value": "speed_print * 0.90"}, "speed_travel": {"value": "170"}, "speed_travel_layer_0": {"value": "speed_travel * 0.70"}, "speed_wall_0": {"value": "35 if speed_print > 35 else speed_print"}, "speed_z_hop": {"value": "speed_travel"}, "support_bottom_distance": {"value": "layer_height"}, "support_bottom_enable": {"value": false}, "support_wall_count": {"value": "1"}, "support_z_distance": {"value": "layer_height"}, "switch_extruder_retraction_amount": {"value": 10}, "switch_extruder_retraction_speeds": {"default_value": 70}, "top_bottom_thickness": {"default_value": 0.8}, "travel_avoid_distance": {"value": "1"}, "travel_avoid_supports": {"value": "True"}, "z_seam_corner": {"value": "'z_seam_corner_weighted'"}}}