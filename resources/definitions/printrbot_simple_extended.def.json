{"version": 2, "name": "Printrbot Simple Metal Extended", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "samsector", "manufacturer": "Printrbot", "file_formats": "text/x-gcode", "platform": "printrbot_simple_metal_upgrade.3mf", "machine_extruder_trains": {"0": "printrbot_simple_extended_extruder_0"}, "platform_offset": [0, -0.3, 0]}, "overrides": {"gantry_height": {"value": "99999"}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 150}, "machine_end_gcode": {"default_value": "M104 S0     ;extruder heater off\nM140 S0     ;heated bed heater off (if you have it)\nM106 S0     ;fan off\nG91         ;relative positioning\nG1 E-1 F300 ;retract the filament a bit\nG1 Z+1 E-5 F9000 ;move Z up a bit and retract even more\nG28 X0 Y0   ;home X/Y, so the head is out of the way\nM84         ;steppers off\nG90         ;absolute positioning"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[55, -20], [55, 99999], [-49, 99999], [-49, -20]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 235}, "machine_name": {"default_value": "Printrbot Simple Metal Extended"}, "machine_start_gcode": {"default_value": "G21       ;metric values\nG90       ;absolute positioning\nM82       ;set extruder to absolute mode\nM106      ;start with the fan on\nG28 X0 Y0 ;home X/Y\nG28 Z0    ;home Z\nG92 E0    ;zero the extruded length\nG29       ;initiate auto bed leveling sequence\n;start cleaning sequance\nG1 X250 Y150 Z15 F4000\nG1 X250 Y150 Z0.30 F1000\nG1 X1 Y150 Z0.25 E15.0 F2000\nG1 X1 Y150 Z0.25 E14.0 F4000\nG1 X1 Y1 Z0.25 F16000\nG1 X1 Y1 Z0.25 E15.0 F4000\nG92 E0\nM107      ;start with the fan off\n;end cleaning sequance\n;G92 X132.4 Y20 ;correct bed origin (G29 changes it)"}, "machine_width": {"default_value": 250}}}