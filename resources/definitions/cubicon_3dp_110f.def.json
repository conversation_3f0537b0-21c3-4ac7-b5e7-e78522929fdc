{"version": 2, "name": "Cubicon Single", "inherits": "cubicon_common", "metadata": {"visible": true, "author": "Cubicon R&D Center", "manufacturer": "Cubicon", "file_formats": "text/x-gcode", "machine_extruder_trains": {"0": "cubicon_3dp_110f_extruder_0"}, "platform_offset": [0, -32.05, -20], "supports_usb_connection": false}, "overrides": {"machine_depth": {"default_value": 190}, "machine_height": {"default_value": 200}, "machine_name": {"default_value": "Cubicon Single"}, "machine_start_gcode": {"default_value": "M911 3DP-110F\nM201 X400 Y400\nM202 X400 Y400\nG28 ; Home\nG1 Z15.0 F6000 ;move the platform down 15mm\n;Prime the extruder\nG92 E0\nG1 F200 E3\nG92 E0"}, "machine_width": {"default_value": 240}, "material_bed_temp_wait": {"default_value": true}}}