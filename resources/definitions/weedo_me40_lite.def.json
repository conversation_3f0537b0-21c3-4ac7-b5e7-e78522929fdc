{"version": 2, "name": "WEEDO ME40 Lite", "inherits": "weedo_base", "metadata": {"visible": true, "author": "WEEDO", "manufacturer": "WEEDO", "file_formats": "text/x-gcode", "platform_offset": [0, 0, 0]}, "overrides": {"machine_depth": {"default_value": 300}, "machine_end_gcode": {"default_value": ";(**** end.gcode for  ME40****)\nM203 Z15\nG1 X0 Y130 F3000\nG92 E0\nG1 E-1 F1680 ; Reduce filament pressure\nM107 ; Turn off fan\n; Disable all extruder\nM104 T0 S0\nG90 ; Absolute positioning\nG92 E0 ; Reset extruder position\nM140 S0 ; Disable heated bed\nM84 ; Turn steppers off"}, "machine_height": {"default_value": 400}, "machine_name": {"default_value": "WEEDO ME40 Lite"}, "machine_start_gcode": {"default_value": ";MachineType:{machine_name}\n;FilamentType:{material_type}\n;InfillDensity:{infill_sparse_density}\n;Extruder0Temperature:{material_print_temperature}\n;BedTemperature:{material_bed_temperature}\n\n;(**** start.gcode for ME40 Lite****)\nM203 Z15\nM104 S170\nG28 ; Home extruder\nG1 Z15\nM107 ; Turn off fan\nG90 ; Absolute positioning\nM82 ; Extruder in absolute mode\nM109 S{material_print_temperature_layer_0}\nG92 E0 ; Reset extruder position\nG1 X140 Y7 Z0.27 F4000\nG1 X40 Y7 Z0.27 E23 F1000\nG92 E0\nM203 Z8"}, "machine_width": {"default_value": 300}, "material_bed_temperature": {"maximum_value": "90", "maximum_value_warning": "70"}, "speed_print": {"value": 60.0}, "speed_support": {"value": "round(speed_print * 0.96, 1)"}, "speed_topbottom": {"value": "round(speed_print * 0.58, 1)"}, "speed_wall": {"value": "speed_print / 2"}}}