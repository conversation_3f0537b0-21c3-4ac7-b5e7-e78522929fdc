{"version": 2, "name": "Blocks Zero", "inherits": "blocks_base", "metadata": {"visible": true, "platform": "blocks_zero_platform.stl", "machine_extruder_trains": {"0": "blocks_zero_extruder_0"}, "quality_definition": "blocks_base"}, "overrides": {"machine_depth": {"default_value": 140}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off\nM140 S0 ;heated bed heater off (if you have it)\nG91 ;relative positioning\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 X-20 Y-20 F6000 ;move Z up a bit and retract filament even more\nG28 X0 Y0 ;move X/Y to min endstops, so the head is out of the way\nM84 ;steppers off\nG90 ;absolute positioning\n"}, "machine_heated_bed": {"default_value": false}, "machine_height": {"default_value": 120}, "machine_name": {"default_value": "Blocks Zero"}, "machine_start_gcode": {"default_value": "G21\nG90 ;absolute positioning\nM82 ;set extruder to absolute mode\nM107 ;start with the fan off\nG28 X0 Y0  ;move X/Y to min endstops\nG28 Z0 ;move Z to min endstops\nG1 X-14 Y0 F6000\nG1 Z0.1\nG92 E0 ;zero the extruded length\nG1 F2000 E10 ;extrude 10mm of feed stock\nG92 E0 ;zero the extruded length again\nG1 Z0.2 F6000\nG1 F6000\nM117 Printing...\n"}, "machine_width": {"default_value": 120}, "retraction_retract_speed": {"value": 50}}}