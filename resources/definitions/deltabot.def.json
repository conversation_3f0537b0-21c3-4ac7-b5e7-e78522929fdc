{"version": 2, "name": "DeltaBot", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Custom", "file_formats": "text/x-gcode", "machine_extruder_trains": {"0": "deltabot_extruder_0"}, "platform_offset": [0, 0, 0]}, "overrides": {"infill_sparse_density": {"default_value": 10}, "layer_height": {"default_value": 0.2}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 150}, "machine_extruder_count": {"default_value": 1}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 150}, "machine_name": {"default_value": "DeltaBot style"}, "machine_shape": {"default_value": "elliptic"}, "machine_width": {"default_value": 150}, "prime_tower_size": {"default_value": 8.660254037844387}, "speed_print": {"default_value": 30}}}