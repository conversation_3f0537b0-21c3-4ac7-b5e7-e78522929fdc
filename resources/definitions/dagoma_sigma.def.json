{"version": 2, "name": "Dagoma Sigma", "inherits": "dagoma_delta", "metadata": {"visible": true, "author": "Dagoma", "manufacturer": "Dagoma", "file_formats": "text/x-gcode", "platform": "dagoma_sigma.stl", "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "dagoma_sigma_extruder"}, "preferred_quality_type": "h0.2", "preferred_variant_name": "0.4mm", "variants_name": "Nozzle"}, "overrides": {"machine_end_gcode": {"default_value": ";End Gcode for {machine_name}\n;Author: Dagoma\n\nM104 S0\nM107\nM140 S0\nG91\nG1 E-1 F300\nG1 Z+3 E-2 F9000\nG90\nG28\n"}, "machine_name": {"default_value": "Dagoma Sigma"}, "machine_start_gcode": {"default_value": ";Start Gcode for {machine_name}\n;Author: <PERSON><PERSON><PERSON>\n\n;Sliced: {date} {time}\n;Estimated print time: {print_time}\n\n;Print speed: {speed_print}mm/s\n;Layer height: {layer_height}mm\n;Wall thickness: {wall_thickness}mm\n;Infill density: {infill_sparse_density}%\n;Infill pattern: {infill_pattern}\n;Support: {support_enable}\n;Print temperature: {material_print_temperature}°C\n;Flow: {material_flow}%\n;Retraction amount: {retraction_amount}mm\n;Retraction speed: {retraction_retract_speed}mm/s\n\nG90 ;absolute positioning\nM104 S120 ;Launch heating up to 120°C\nM106 S255 ;Activating layers fans\nG29 ;Homing and Calibration\nM107 ;Off Ventilateur\nM109 S{material_print_temperature} U-55 X55 V-85 Y-85 W0.26 Z0.26 ;Temperature for the first layer only\nM82 ;Set extruder to absolute mode\nG92 E0 ;Zero the extruded length\nG1 F200 E6 ;Extrude 10mm of feed stock\nG92 E0 ;Zero the extruded length again\nG1 F200 E-3.5\nG0 Z0.15\nG0 X10\nG0 Z3\nG1 F{speed_travel}\nM117 Printing...\n"}, "speed_travel": {"value": 150}}}