{"version": 2, "name": "Anycubic i3 Mega S/Pro", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON><PERSON>", "manufacturer": "Anycubic", "file_formats": "text/x-gcode", "platform": "anycubic_i3_mega_s_platform.3mf", "has_machine_quality": true, "has_materials": true, "has_variants": false, "machine_extruder_trains": {"0": "anycubic_i3_mega_s_extruder_0"}, "preferred_quality_type": "normal"}, "overrides": {"acceleration_enabled": {"value": false}, "acceleration_print": {"value": 1500}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": 3000}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adhesion_type": {"value": "'none' if support_enable else 'skirt'"}, "brim_replaces_support": {"value": false}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_fan_speed": {"value": 100}, "cool_fan_speed_0": {"value": 30}, "cool_min_layer_time": {"value": 10}, "gantry_height": {"value": "0"}, "infill_before_walls": {"value": false}, "infill_overlap": {"value": 15.0}, "infill_sparse_density": {"value": 25}, "jerk_enabled": {"value": true}, "jerk_print": {"value": 8}, "jerk_travel": {"value": 10}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 3000}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 210}, "machine_end_gcode": {"default_value": "M104 S0                                    ; Extruder off \nM140 S0                                    ; Heatbed off \nM107                                       ; Fan off \nG91                                        ; relative positioning \nG1 E-5 F300                                ; retract a little \nG1 Z+10 E-5 ; X-20 Y-20 F{travel_xy_speed} ; lift print head \nG28 X0 Y0                                  ; homing \nG1 Y180 F2000                              ; reset feedrate \nM84                                        ; disable stepper motors \nG90                                        ; absolute positioning \nM300 S440 P200                             ; Make Print Completed Tones \nM300 S660 P250                             ; beep \nM300 S880 P300                             ; beep"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 205}, "machine_max_acceleration_e": {"value": 10000}, "machine_max_acceleration_x": {"value": 3000}, "machine_max_acceleration_y": {"value": 2000}, "machine_max_acceleration_z": {"value": 60}, "machine_max_feedrate_x": {"default_value": 500}, "machine_max_feedrate_y": {"default_value": 500}, "machine_max_feedrate_z": {"default_value": 8}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.4}, "machine_name": {"default_value": "Anycubic i3 Mega S/Pro"}, "machine_start_gcode": {"default_value": ";Profil Homepage: https://github.com/NilsRo/Cura_Anycubic_MegaS_Profile\n\n;Slicer Information - (Support for OctoPrint Slicer Estimator)\n;Slicer info:material_guid;{material_guid}\n;Slicer info:material_id;{material_id}\n;Slicer info:material_brand;{material_brand}\n;Slicer info:material_name;{material_name}\n;Slicer info:filament_cost;{filament_cost}\n;Slicer info:material_bed_temperature;{material_bed_temperature}\n;Slicer info:material_bed_temperature_layer_0;{material_bed_temperature_layer_0}\n;Slicer info:material_print_temperature;{material_print_temperature}\n;Slicer info:material_print_temperature_layer_0;{material_print_temperature_layer_0}\n;Slicer info:material_flow;{material_flow}\n;Slicer info:layer_height;{layer_height}\n;Slicer info:machine_nozzle_size;{machine_nozzle_size}\n;Slicer info:wall_thickness;{wall_thickness}\n;Slicer info:speed_print;{speed_print}\n;Slicer info:speed_topbottom;{speed_topbottom}\n;Slicer info:travel_speed;{travel_speed}\n;Slicer info:support;{support}\n;Slicer info:retraction_speed;{retraction_speed}\n;Slicer info:retraction_amount;{retraction_amount}\n;Slicer info:layer_height;{layer_height}\n;Slicer info:infill_pattern;{infill_pattern}\n;Slicer info:infill_sparse_density;{infill_sparse_density}\n;Slicer info:cool_fan_enabled;{cool_fan_enabled}\n;Slicer info:cool_fan_speed;{cool_fan_speed}\n;Slicer info:sliced_at;{day} {date} {time}\nG21                                        ; metric values \nG90                                        ; absolute positioning \nM82                                        ; set extruder to absolute mode \nM900 K0                                    ; disable lin. adv. if not set in GCODE\nM107                                       ; start with the fan off \nM140 S{material_bed_temperature_layer_0}   ; Start heating the bed \nG4 S60                                     ; wait 1 minute \nM104 S{material_print_temperature_layer_0} ; start heating the hot end \nM190 S{material_bed_temperature_layer_0}   ; wait for bed \nM109 S{material_print_temperature_layer_0} ; wait for hotend \nM300 S1000 P500                            ; BEEP heating done \nG28 X0 Y10 Z0                              ; move X/Y to min endstops \nM420 S1                                    ; Enable leveling \nM420 Z2.0                                  ; Set leveling fading height to 2 mm \nG0 Z0.15                                   ; lift nozzle a bit \nG92 E0                                     ; zero the extruded length \nG1 X50 E20 F500                            ; Extrude 20mm of filament in a 5cm line. \nG92 E0                                     ; zero the extruded length again \nG1 E-2 F500                                ; Retract a little \nG1 X50 F500                                ; wipe away from the filament line\nG1 X100 F9000                              ; Quickly wipe away from the filament line"}, "machine_width": {"default_value": 210}, "material_bed_temperature": {"maximum_value_warning": 110}, "material_bed_temperature_layer_0": {"maximum_value_warning": 110}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_print_temperature": {"maximum_value_warning": 260}, "material_print_temperature_layer_0": {"maximum_value_warning": 260, "value": "material_print_temperature + 5"}, "meshfix_maximum_deviation": {"value": 0.05}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "2 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": "True"}, "retraction_amount": {"value": 6}, "retraction_combing": {"value": "'off'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_hop": {"value": 0.075}, "retraction_hop_enabled": {"value": true}, "retraction_hop_only_when_collides": {"value": true}, "retraction_min_travel": {"value": 1.5}, "retraction_prime_speed": {"maximum_value_warning": 60}, "retraction_retract_speed": {"maximum_value_warning": 60}, "retraction_speed": {"maximum_value_warning": 60, "value": 30}, "skirt_brim_speed": {"value": "speed_layer_0"}, "skirt_gap": {"value": 5.0}, "skirt_line_count": {"value": 2}, "speed_layer_0": {"value": "speed_topbottom if speed_topbottom < 20 else 20"}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": 50.0}, "speed_roofing": {"value": "speed_topbottom"}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_travel": {"maximum_value": 200.0, "maximum_value_warning": 150.0, "value": 100.0}, "speed_travel_layer_0": {"value": "speed_travel"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"value": 8}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width / 2.0 / layer_height)))"}, "support_brim_enable": {"value": true}, "support_brim_width": {"value": 4}, "support_infill_rate": {"value": "0 if support_enable and support_structure == 'tree' else 20"}, "support_interface_density": {"value": 33.333}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"value": "'grid'"}, "support_pattern": {"value": "'zigzag'"}, "support_structure": {"value": "'tree'"}, "support_type": {"value": "'buildplate' if support_structure == 'tree' else 'everywhere'"}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height * 2"}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * math.floor(1.2 / layer_height)"}, "travel_avoid_other_parts": {"value": true}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_thickness": {"value": "line_width * 3 if line_width < 0.6 else line_width * 2"}}}