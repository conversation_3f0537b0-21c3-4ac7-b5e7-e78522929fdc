{"version": 2, "name": "JGAurora A6", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "CrissR", "manufacturer": "JGAurora", "file_formats": "text/x-gcode", "platform": "jgaurora_a6_platform.stl", "has_machine_quality": true, "has_variants": false, "machine_extruder_trains": {"0": "jgaurora_a6_extruder_0"}, "preferred_quality_type": "normal", "quality_definition": "jgaurora_a6", "supports_network_connection": false, "supports_usb_connection": true}, "overrides": {"adhesion_type": {"default_value": "skirt"}, "gantry_height": {"value": 10}, "layer_height": {"default_value": 0.16}, "layer_height_0": {"default_value": 0.2}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 200}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off\nM140 S0;heated bed heater off\nG91;relative positioning\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 X-20 Y-20 F{travel_speed} ;move Z up a bit and retract filament even more\nG28 X0 Y0;move X/Y to min endstops, so the head is out of the way\nM84 ;steppers off\nG90;absolute positioning\nM104 S0 ; turn off extruder\nM140 S0 ; turn off heatbed\nM107 ; turn off fan\nG1 X178 Y180 F4200 ; park print head\nM84 ; disable motors"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 200}, "machine_name": {"default_value": "JGAurora A6"}, "machine_nozzle_size": {"default_value": 0.4}, "machine_start_gcode": {"default_value": "M190 S{print_bed_temperature} ;bed temperature line\nM109 S{print_temperature} ;temperature line\nG21 ;metric values\nG90 ;absolute positioning\nM82 ;set extruder to absolute mode\nM107;start with the fan off\nG28 X0 Y0 ;move X/Y to min endstops\nG28 Z0 ;move Z to min endstops\nG1 Z15.0 F{travel_speed} ;move the platform down 15mm\nG92 E0 ;zero the extruded length\nF200 E3;extrude 3mm of feed stock\nG92 E0 ;zero the extruded length again\nG1 F{travel_speed}\nG1 Z0.0 F{travel_speed}\nM117 Printing... ;LCD Message"}, "machine_width": {"default_value": 300}, "material_diameter": {"default_value": 1.75}, "retraction_amount": {"default_value": 4}, "retraction_enable": {"default_value": true}, "retraction_speed": {"default_value": 45}, "speed_print": {"default_value": 60}}}