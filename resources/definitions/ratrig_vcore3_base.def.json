{"version": 2, "name": "RatRig V-Core 3", "inherits": "ratrig_base", "metadata": {"visible": false, "has_machine_quality": true, "has_variants": true, "machine_extruder_trains": {"0": "ratrig_base_extruder_0"}, "preferred_variant_name": "0.4mm Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_layer_0": {"value": "acceleration_topbottom"}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_topbottom": {"value": "acceleration_print / 3"}, "acceleration_travel": {"value": 3000}, "acceleration_travel_layer_0": {"value": "acceleration_travel / 3"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "brim_replaces_support": {"value": false}, "brim_width": {"value": "3"}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 2}, "fill_outline_gaps": {"value": false}, "gantry_height": {"value": 30}, "infill_before_walls": {"value": false}, "infill_overlap": {"value": 30}, "infill_pattern": {"value": "'lines' if infill_sparse_density > 50 else 'cubic'"}, "infill_wipe_dist": {"value": 0}, "layer_height": {"default_value": 0.2}, "layer_height_0": {"default_value": 0.2}, "machine_acceleration": {"value": 3000}, "machine_end_gcode": {"default_value": "END_PRINT"}, "machine_extruder_count": {"default_value": 1}, "machine_head_with_fans_polygon": {"default_value": [[-40, 90], [-40, -30], [40, -30], [40, 90]]}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 9000}, "machine_max_acceleration_y": {"value": 9000}, "machine_max_acceleration_z": {"value": 100}, "machine_max_feedrate_e": {"value": 60}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 10}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 5}, "machine_max_jerk_z": {"value": 0.4}, "machine_name": {"default_value": "RatRig V-Core 3"}, "machine_shape": {"default_value": "rectangular"}, "machine_show_variants": {"default_value": true}, "machine_start_gcode": {"default_value": "START_PRINT EXTRUDER_TEMP={material_print_temperature_layer_0} BED_TEMP={material_bed_temperature_layer_0}"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": 2}, "optimize_wall_printing_order": {"value": true}, "retraction_amount": {"value": "machine_nozzle_size * 2"}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 10}, "retraction_speed": {"value": 40}, "roofing_layer_count": {"value": 1}, "skin_overlap": {"value": 18}, "skirt_brim_minimal_length": {"default_value": 30}, "skirt_gap": {"value": 10}, "skirt_line_count": {"value": 3}, "speed_layer_0": {"value": "math.floor(speed_print * 3 / 10)"}, "speed_print": {"value": 100}, "speed_roofing": {"value": "math.floor(speed_print * 3 / 10)"}, "speed_support": {"value": "math.floor(speed_print * 3 / 10)"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "math.floor(speed_print / 2)"}, "speed_travel": {"value": 250}, "speed_travel_layer_0": {"value": "100 if speed_layer_0 < 20 else 150 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"value": 5}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width/2.0/layer_height)))"}, "support_brim_width": {"value": 4}, "support_infill_rate": {"value": "0 if support_enable and support_structure == 'tree' else 20"}, "support_interface_density": {"value": 33.333}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"value": "'grid'"}, "support_pattern": {"value": "'zigzag'"}, "support_use_towers": {"value": false}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height * 2"}, "top_bottom_pattern": {"value": "'lines'"}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3"}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_corner": {"value": "'z_seam_corner_weighted'"}, "z_seam_type": {"value": "'back'"}}}