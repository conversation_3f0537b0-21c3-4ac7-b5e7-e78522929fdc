{"version": 2, "name": "<PERSON><PERSON>", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "MUX team", "manufacturer": "Hellbot", "file_formats": "text/x-gcode", "platform": "hellbot_adonis.obj", "has_materials": true, "has_textured_buildplate": true, "machine_extruder_trains": {"0": "hellbot_adonis_extruder"}, "platform_offset": [0, -1, 0], "platform_texture": "hellbot.png"}, "overrides": {"machine_depth": {"default_value": 160}, "machine_height": {"default_value": 160}, "machine_name": {"default_value": "<PERSON><PERSON>"}, "machine_width": {"default_value": 160}}}