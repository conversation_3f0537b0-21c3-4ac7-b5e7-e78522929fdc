{"version": 2, "name": "Hyper S", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Mixware", "manufacturer": "Mixware", "file_formats": "text/x-gcode", "platform": "mixware_hyper_s_platform.stl", "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "mixware_hyper_s_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality": "coarse"}, "overrides": {"acceleration_enabled": {"default_value": false}, "acceleration_print": {"default_value": 1000}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": 1000}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"default_value": 0.04}, "adaptive_layer_height_variation_step": {"default_value": 0.04}, "adhesion_type": {"default_value": "skirt"}, "brim_replaces_support": {"default_value": false}, "brim_width": {"default_value": 3}, "cool_fan_enabled": {"default_value": true}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"default_value": 10}, "fill_outline_gaps": {"default_value": false}, "gantry_height": {"value": 25}, "infill_before_walls": {"default_value": false}, "infill_overlap": {"value": 30.0}, "infill_sparse_density": {"default_value": 15}, "infill_wipe_dist": {"value": 0.0}, "ironing_line_spacing": {"default_value": 0.4}, "ironing_pattern": {"default_value": "concentric"}, "jerk_enabled": {"default_value": false}, "jerk_print": {"default_value": 10}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "line_width": {"value": 0.4}, "machine_acceleration": {"default_value": 500}, "machine_depth": {"default_value": 300}, "machine_end_gcode": {"default_value": "G91; relative positioning\nG1 Z1.0 F3000 ; move z up little to prevent scratching of print\nG90; absolute positioning\nG1 X0 Y200 F1000 ; prepare for part removal\nM104 S0; turn off extruder\nM140 S0 ; turn off bed\nG1 X0 Y220 F1000 ; prepare for part removal\nM84 ; disable motors\nM106 S0 ; turn off fan"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 400}, "machine_max_acceleration_e": {"default_value": 500}, "machine_max_acceleration_x": {"default_value": 500}, "machine_max_acceleration_y": {"default_value": 500}, "machine_max_acceleration_z": {"default_value": 100}, "machine_max_feedrate_e": {"default_value": 50}, "machine_max_feedrate_x": {"default_value": 500}, "machine_max_feedrate_y": {"default_value": 500}, "machine_max_feedrate_z": {"default_value": 10}, "machine_max_jerk_e": {"default_value": 5}, "machine_max_jerk_xy": {"default_value": 10}, "machine_max_jerk_z": {"default_value": 0.4}, "machine_name": {"default_value": "Hyper S"}, "machine_start_gcode": {"default_value": "M140 S{material_bed_temperature} ; Heat bed\nM109 S{material_print_temperature} ; Heat nozzle\nM190 S{material_bed_temperature} ; Wait for bed heating\nG28 ; home all axes\nM117 Purge extruder\nG92 E0 ; reset extruder\nG1 Z5.0 F1000 ; move z up little to prevent scratching of surface\nG1 X0.1 Y20 Z0.3 F5000.0 ; move to start-line position\nG1 X0.1 Y100.0 Z0.3 F1500.0 E15 ; draw 1st line\nG1 X0.4 Y100.0 Z0.3 F5000.0 ; move to side a little\nG1 X0.4 Y20 Z0.3 F1500.0 E30 ; draw 2nd line\nG92 E0 ; reset extruder\nG1 Z5.0 F1000 ; move z up little to prevent scratching of surface"}, "machine_width": {"default_value": 300}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_flow": {"default_value": 100}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "material_print_temperature": {"maximum_value_warning": "330"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"default_value": 10}, "minimum_support_area": {"default_value": 2}, "optimize_wall_printing_order": {"default_value": "True"}, "raft_airgap": {"default_value": 0.24}, "raft_margin": {"default_value": 3, "minimum_value_warning": "0.01"}, "retraction_amount": {"default_value": 2}, "retraction_combing": {"value": "'off'"}, "retraction_combing_max_distance": {"default_value": 0.5}, "retraction_count_max": {"default_value": 100}, "retraction_extrusion_window": {"maximum_value_warning": "20", "value": 10}, "retraction_hop": {"default_value": 0.2}, "retraction_hop_enabled": {"default_value": false}, "retraction_min_travel": {"value": 1.5}, "retraction_prime_speed": {"maximum_value": "200", "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_retract_speed": {"maximum_value": "200", "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_speed": {"default_value": 40, "maximum_value": "200", "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "skin_overlap": {"value": 10.0}, "skirt_line_count": {"default_value": 3}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"default_value": 40.0}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "speed_print / 2"}, "speed_travel": {"value": "120.0 if speed_print < 60 else 250.0 if speed_print > 100 else speed_print * 2.5"}, "speed_travel_layer_0": {"value": "60 if speed_layer_0 < 20 else 120 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"default_value": 5}, "support_angle": {"default_value": 60}, "support_bottom_stair_step_height": {"value": 0.2}, "support_brim_enable": {"value": false}, "support_enable": {"default_value": true}, "support_interface_density": {"default_value": 80}, "support_interface_enable": {"default_value": true}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"default_value": "grid"}, "support_pattern": {"default_value": "zigzag"}, "support_type": {"default_value": "buildplate"}, "support_wall_count": {"value": 1}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"default_value": "xy_overrides_z"}, "support_z_distance": {"value": "layer_height"}, "top_bottom_pattern": {"default_value": "zigzag"}, "travel_avoid_other_parts": {"default_value": false}, "travel_avoid_supports": {"default_value": true}, "travel_retract_before_outer_wall": {"default_value": true}, "wall_0_wipe_dist": {"value": 0.0}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_corner": {"default_value": "z_seam_corner_weighted"}, "z_seam_type": {"default_value": "back"}}}