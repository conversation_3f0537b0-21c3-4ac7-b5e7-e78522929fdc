{"version": 2, "name": "Zav <PERSON> Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "<PERSON><PERSON>, <PERSON> (C)", "manufacturer": "Zav Co., Ltd.", "file_formats": "text/x-gcode", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "imade3d_petg", "imade3d_pla", "innofill_innoflex60"], "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "zav_extruder_1", "1": "zav_extruder_2"}, "preferred_material": "bestfilament_abs_skyblue", "preferred_quality_type": "ZAV_layer_020", "preferred_variant_name": "0.40mm_ZAV_Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"adhesion_type": {"value": "'brim'"}, "bottom_thickness": {"value": "layer_height*3 if layer_height > 0.15 else 0.8"}, "bridge_settings_enabled": {"value": "True"}, "brim_width": {"value": "5"}, "cool_fan_full_at_height": {"value": "layer_height*2"}, "cool_lift_head": {"value": "True"}, "cool_min_layer_time": {"value": "15"}, "cool_min_layer_time_fan_speed_max": {"value": "20"}, "cool_min_speed": {"value": "15"}, "expand_skins_expand_distance": {"value": "3"}, "gantry_height": {"value": 999999}, "infill_before_walls": {"value": "False"}, "infill_enable_travel_optimization": {"value": "True"}, "infill_line_width": {"value": "round(line_width * 1.1, 2)"}, "infill_pattern": {"value": "'gyroid'"}, "layer_height_0": {"value": "layer_height"}, "machine_buildplate_type": {"value": "'glass'"}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 200}, "machine_end_gcode": {"default_value": ";---- Ending Script Start ----\nM104 S0                                        ;extruder heater off\nM140 S0                                        ;heated bed heater off (if you have it)\nG91                                               ;relative positioning\nG1 E-4 F300                                  ;retract the filament a bit before lifting the nozzle to release some of the pressure\nG1 Z+0.5 E-5 X-20 Y-20 F5000    ;move Z up a bit and retract filament even more\nG28 Z0                                          ;move bed down\nG28 X0 Y0                                     ;move X/Y to min endstops so the head is out of the way\nM84                                              ;steppers off\nG90                                              ;absolute positioning\nM107                                            ;switch off cooling fan\nM355 S0 P0                                  ;switch off case light\n;---- Ending Script End ----\n"}, "machine_extruder_count": {"value": 1}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-26, 41], [-26, -21], [36, -21], [36, 41]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 270}, "machine_name": {"default_value": "ZAV Base Printer"}, "machine_shape": {"default_value": "rectangular"}, "machine_start_gcode": {"default_value": ";---- Starting <PERSON><PERSON>t Start ----\nG90                     ;absolute positioning\nM82                     ;set extruder to absolute mode\nM107                   ;start with the fan off\nG28 Z0                ;move Z to min endstops\nG28 X0 Y0           ;move X/Y to min endstops\nG92 E0                ;zero the extruded length\nG1  F5000           ;set speed\nG1 Y40                ;move to start position Y\nM117 Printing...\n;---- Starting Script End ----\n"}, "machine_width": {"default_value": 300}, "material_diameter": {"default_value": 1.75}, "optimize_wall_printing_order": {"value": "True"}, "retraction_amount": {"value": "4"}, "retraction_combing": {"value": "'all'"}, "retraction_combing_max_distance": {"value": "10"}, "retraction_min_travel": {"value": "3"}, "skin_line_width": {"value": "round(line_width * 1.0, 2)"}, "skin_outline_count": {"value": "0"}, "skirt_brim_line_width": {"value": "round(line_width * 1.1, 2)"}, "speed_layer_0": {"value": "25"}, "speed_print": {"value": "80"}, "speed_topbottom": {"value": "50"}, "speed_travel_layer_0": {"value": "40"}, "support_angle": {"value": "65"}, "support_brim_enable": {"value": "True"}, "support_enable": {"value": "True"}, "support_infill_rate": {"value": "20"}, "support_offset": {"value": "2"}, "top_bottom_pattern": {"value": "'zigzag'"}, "top_bottom_pattern_0": {"value": "'zigzag'"}, "travel_avoid_other_parts": {"value": "False"}, "z_seam_type": {"value": "'shortest'"}, "zig_zaggify_infill": {"value": "True"}}}