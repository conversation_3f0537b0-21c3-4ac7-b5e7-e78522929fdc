{"version": 2, "name": "Anycubic Predator", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Anycubic", "file_formats": "text/x-gcode", "platform": "predator_platform.stl", "has_machine_materials": false, "has_machine_quality": true, "has_materials": true, "has_variant_materials": false, "has_variants": false, "machine_extruder_trains": {"0": "predator_extruder_0"}, "platform_offset": [0, 0, 0], "platform_texture": "anycubic_predator.png", "preferred_quality_type": "normal", "quality_definition": "predator", "supports_usb_connection": true}, "overrides": {"coasting_enable": {"default_value": true}, "gantry_height": {"value": 455}, "line_width": {"value": "round(machine_nozzle_size * 0.875, 2)"}, "machine_acceleration": {"default_value": 3000}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 370}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off \nM140 S0 ;heated bed heater off \nG91 ;relative positioning \nG92 E0 ;zero the extruded length \nG1 E-2 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure \nG92 E0 ;zero the extruded length \n;M107 ;fan off \nG1 Z+0.5 E-5 ;move Z up a bit and retract filament even more \n;G1 X0.0 Y0.0 Z455.0 F4500 \nG28 ;Home all axes (max endstops) \nG90 ;absolute positioning \n;M109 S60 ; wait for extruder temp to drop to 60 \n;M84 ;steppers off \n;M81 ;turn off printer"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 455}, "machine_max_acceleration_e": {"default_value": 10000}, "machine_max_acceleration_x": {"default_value": 800}, "machine_max_acceleration_y": {"default_value": 800}, "machine_max_acceleration_z": {"default_value": 800}, "machine_max_feedrate_e": {"default_value": 50}, "machine_max_feedrate_x": {"default_value": 150}, "machine_max_feedrate_y": {"default_value": 150}, "machine_max_feedrate_z": {"default_value": 150}, "machine_max_jerk_e": {"default_value": 5}, "machine_max_jerk_xy": {"default_value": 20}, "machine_max_jerk_z": {"default_value": 20}, "machine_nozzle_size": {"default_value": 0.4, "maximum_value": 0.8, "minimum_value": 0.15}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": "G21 ;metric values \nG90 ;absolute positioning \nM82 ;set extruder to absolute mode \nM107 ;start with the fan off \nG28 ;Home \nG1 Z15.0 F1000;short move \nG92 E0 ;zero the extruded length \nG1 F200 E3 ;extrude 3mm of feed stock adjust so I don't have to remove any filament \nG92 E0 ;zero the extruded length again \nG1 F4000 ;move to bed \n;Put printing message on LCD screen \nM117 Printing... \n;G5; this is in order to resume on Power failure (only works on SD prints)"}, "machine_width": {"default_value": 370}, "material_bed_temperature_layer_0": {"value": "material_bed_temperature + 5"}, "material_print_temperature_layer_0": {"value": "material_print_temperature + 5"}, "retract_at_layer_change": {"default_value": true}, "retraction_amount": {"default_value": 4}, "retraction_hop_enabled": {"default_value": false}, "retraction_speed": {"default_value": 50}}}