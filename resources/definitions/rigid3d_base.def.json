{"version": 2, "name": "Rigid3D Base Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "Mehmet SUTAŞ", "manufacturer": "Rigid3D", "file_formats": "text/x-gcode", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "rigid3d_base_extruder_0"}, "preferred_material": "generic_pla_175", "supported_actions": ["MachineSettingsAction"]}, "overrides": {"acceleration_enabled": {"value": "False"}, "acceleration_print": {"value": 600}, "acceleration_travel": {"value": 600}, "adaptive_layer_height_variation": {"value": 0.16}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "expand_skins_expand_distance": {"value": 0.3}, "infill_pattern": {"value": "'zigzag'"}, "infill_sparse_density": {"value": 10}, "inset_direction": {"value": "'outside_in'"}, "jerk_enabled": {"value": "False"}, "jerk_print": {"value": 10}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 600}, "machine_max_acceleration_e": {"value": 1000}, "machine_max_acceleration_x": {"value": 600}, "machine_max_acceleration_y": {"value": 600}, "machine_max_acceleration_z": {"value": 300}, "machine_max_feedrate_e": {"value": 40}, "machine_max_feedrate_x": {"value": 100}, "machine_max_feedrate_y": {"value": 100}, "machine_max_feedrate_z": {"value": 8}, "machine_max_jerk_xy": {"value": 10.0}, "machine_max_jerk_z": {"value": 0.3}, "machine_name": {"default_value": "Rigid3D Base Printer"}, "material_bed_temperature_layer_0": {"value": "material_bed_temperature"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "minimum_interface_area": {"value": 5.0}, "minimum_support_area": {"value": "5 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": "True"}, "retraction_amount": {"value": 1.5}, "retraction_combing": {"value": "'all'"}, "retraction_combing_max_distance": {"value": 30}, "skin_monotonic": {"value": "True"}, "skirt_gap": {"value": 5.0}, "skirt_line_count": {"value": 2}, "speed_support_interface": {"value": "speed_support"}, "speed_topbottom": {"value": "speed_print"}, "speed_travel": {"value": 80.0}, "speed_z_hop": {"default_value": 8.0}, "support_bottom_stair_step_height": {"value": 0.4}, "support_brim_enable": {"value": "True"}, "support_brim_width": {"value": 4}, "support_infill_rate": {"value": 20}, "support_interface_density": {"value": 70}, "support_interface_enable": {"value": true}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height * 2"}, "top_bottom_pattern": {"value": "'zigzag'"}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3"}, "travel_retract_before_outer_wall": {"value": "True"}, "wall_thickness": {"value": "line_width * 2"}, "xy_offset_layer_0": {"value": -0.2}, "z_seam_position": {"value": "'backleft'"}, "z_seam_type": {"value": "'back'"}}}