{"version": 2, "name": "ELEGOO NEPTUNE 3 Pro", "inherits": "elegoo_base", "metadata": {"visible": true, "author": "NARUTO", "quality_definition": "elegoo_base"}, "overrides": {"infill_overlap": {"value": "0 if infill_sparse_density < 40.01 and infill_pattern != 'concentric' else -5"}, "machine_depth": {"default_value": 235}, "machine_end_gcode": {"default_value": "G91 ;Relative positioning\nG1 E-2 F2700 ;Retract a bit\nG1 E-8 X5 Y5 Z3 F3000 ;Retract\nG90 ;Absolute positioning\nG1 X0 Y{machine_depth} ;Present print\nM106 S0 ;Turn-off fan\nM104 S0 ;Turn-off hotend\nM140 S0 ;Turn-off bed\nM84 X Y E ;Disable all steppers but Z"}, "machine_head_with_fans_polygon": {"value": [[-55, 30], [55, 30], [55, -35], [-55, -35]]}, "machine_height": {"default_value": 280}, "machine_name": {"default_value": "ELEGOO NEPTUNE 3 Pro"}, "machine_start_gcode": {"default_value": ";ELEGOO NEPTUNE 3 Pro\nM220 S100 ;Set the feed speed to 100%\nM221 S100 ;Set the flow rate to 100%\nG90\nG28 ;home\n;M420 S1 Z10;Uncomment to enable progressive compensation height of 10mm\nG92 E0 ;Reset Extruder\nG1 Z0.45 F300\nG1 X1.5 Y20 F5000.0 ;Move to start position\nG1 Y120.0 F600.0 E15 ;Draw the first line\nG1 X0.5 F1000.0 ;Move to side a little\nG1 Y20 F600 E30 ;Draw the second line\nG92 E0 ;Reset Extruder"}, "machine_width": {"default_value": 235}, "material_bed_temperature": {"value": "default_material_bed_temperature + 10"}, "retraction_amount": {"value": 1}, "retraction_count_max": {"value": 80}, "retraction_prime_speed": {"resolve": 25}, "retraction_retract_speed": {"resolve": 25}, "retraction_speed": {"resolve": 25}, "speed_travel": {"value": "150 if speed_print < 100 else speed_print * 1.5"}, "speed_travel_layer_0": {"value": "100"}}}