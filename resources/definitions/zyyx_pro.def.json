{"version": 2, "name": "ZYYX Pro", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON>", "manufacturer": "ZYYX Labs AB", "file_formats": "application/x3g", "platform": "zyyx_pro_platform.stl", "exclude_materials": ["3D-Fuel_PLA_PRO_Black", "3D-Fuel_PLA_SnapSupport", "bestfilament_abs_skyblue", "bestfilament_petg_orange", "bestfilament_pla_green", "leapfrog_abs_natural", "leapfrog_epla_natural", "leapfrog_pva_natural", "goofoo_abs", "goofoo_asa", "goofoo_bronze_pla", "goofoo_emarble_pla", "goofoo_esilk_pla", "goofoo_hips", "goofoo_pa_cf", "goofoo_pa", "goofoo_pc", "goofoo_peek", "goofoo_petg", "goofoo_pla", "goofoo_pva", "goofoo_tpe_83a", "goofoo_tpu_87a", "goofoo_tpu_95a", "emotiontech_abs", "emotiontech_absx", "emotiontech_acetate", "emotiontech_asax", "emotiontech_bvoh", "emotiontech_copa", "emotiontech_hips", "emotiontech_nylon_1030", "emotiontech_nylon_1030cf", "emotiontech_nylon_1070", "emotiontech_pa6cf", "emotiontech_pa6gf", "emotiontech_pc", "emotiontech_pekk", "emotiontech_petg", "emotiontech_pla", "emotiontech_pla_hr_870", "emotiontech_pva-m", "emotiontech_pva-s", "emotiontech_tpu98a", "eryone_petg", "eryone_pla_matte", "eryone_pla", "eryone_tpu", "eSUN_PETG_Black", "eSUN_PETG_Grey", "eSUN_PETG_Purple", "eSUN_PLA_PRO_Black", "eSUN_PLA_PRO_Grey", "eSUN_PLA_PRO_Purple", "eSUN_PLA_PRO_White", "Extrudr_GreenTECPro_Anthracite_175", "Extrudr_GreenTECPro_Black_175", "Extrudr_GreenTECPro_Blue_175", "Extrudr_GreenTECPro_Nature_175", "Extrudr_GreenTECPro_Red_175", "Extrudr_GreenTECPro_Silver_175", "Extrudr_GreenTECPro_White_175", "verbatim_bvoh_175", "Vertex_Delta_ABS", "Vertex_Delta_PET", "Vertex_Delta_PLA", "Vertex_Delta_TPU", "chromatik_pla", "dsm_arnitel2045_175", "dsm_novamid1070_175", "fabtotum_abs", "fabtotum_nylon", "fabtotum_pla", "fabtotum_tpu", "fdplast_abs_tomato", "fdplast_petg_gray", "fdplast_pla_olive", "fiberlogy_hd_pla", "filo3d_pla", "filo3d_pla_green", "filo3d_pla_red", "ideagen3D_ToughPLA", "imade3d_petg_green", "imade3d_petg_pink", "imade3d_pla_green", "imade3d_pla_pink", "imade3d_petg_175", "imade3d_pla_175", "innofill_innoflex60_175", "layer_one_black_pla", "layer_one_dark_gray_pla", "layer_one_white_pla", "octofiber_pla", "polyflex_pla", "polymax_pla", "polyplus_pla", "polywood_pla", "redd_abs", "redd_asa", "redd_hips", "redd_nylon", "redd_petg", "redd_pla", "redd_tpe", "tizyx_abs", "tizyx_flex", "tizyx_petg", "tizyx_pla_bois", "tizyx_pla", "tizyx_pva", "ultimaker_abscf_175", "ultimaker_absr_175", "ultimaker_asa_175", "ultimaker_nylon12-cf_175", "ultimaker_pla_175", "ultimaker_pva_175", "ultimaker_rapidrinse_175", "ultimaker_sr30_175", "ultimaker_tough_pla_175", "Vertex_Delta_ABS", "Vertex_Delta_PET", "Vertex_Delta_PLA_Glitter", "Vertex_Delta_PLA_Mat", "Vertex_Delta_PLA_Satin", "Vertex_Delta_PLA_Wood", "Vertex_Delta_PLA", "Vertex_Delta_TPU", "volumic_abs_ultra", "volumic_arma_ultra", "volumic_asa_ultra", "volumic_br80_ultra", "volumic_bumper_ultra", "volumic_cu80_ultra", "volumic_flex93_ultra", "volumic_medical_ultra", "volumic_nylon_ultra", "volumic_pekk_carbone", "volumic_petg_ultra", "volumic_petgcarbone_ultra", "volumic_pla_ultra", "volumic_pp_ultra", "volumic_strong_ultra", "volumic_support_ultra", "xyzprinting_abs", "xyzprinting_antibact_pla", "xyzprinting_carbon_fiber", "xyzprinting_colorinkjet_pla", "xyzprinting_flexible", "xyzprinting_metallic_pla", "xyzprinting_nylon", "xyzprinting_pahtcf15", "xyzprinting_pc", "xyzprinting_petcf15", "xyzprinting_petg", "xyzprinting_pla", "xyzprinting_ppgf30", "xyzprinting_tough_pla", "xyzprinting_tpu", "zyyx_abs-mm", "zyyx_abs-perf", "zyyx_flex95a", "zyyx_petg", "zyyx_pla", "zyyx_pro_flex", "zyyx_pro_pla", "zyyx_pronylon"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine": "zyyx_pro", "machine_extruder_trains": {"0": "zyyx_pro_extruder"}, "machine_x3g_variant": "z", "preferred_material": "generic_pla", "preferred_variant_name": "Carbon0.6", "quality_definition": "zyyx_pro", "setting_version": 3, "variants_name": "SwiftTool"}, "overrides": {"gantry_height": {"value": "10"}, "infill_overlap": {"value": "12 if infill_sparse_density < 95 and infill_pattern != 'concentric' else 0"}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 228}, "machine_disallowed_areas": {"default_value": [[[-58, 117.5], [-58, 108], [-50, 108], [-50, 117.5]], [[119, 117.5], [119, 108], [140, 108], [140, 117.5]], [[-58, -117.5], [-58, -108], [-50, -108], [-50, -117.5]], [[119, -117.5], [119, -108], [140, -108], [140, -117.5]]]}, "machine_end_gcode": {"default_value": "; ZYYX 3D Printer end gcode\nM73 P100 ; end build progress\nG0 Z195 F1000 ; send Z axis to bottom of machine\nM104 S0 T0 ; cool down extruder\nM127 ; stop blower fan\nG162 X Y F3000 ; home XY maximum\nM18 ; disable stepper\nM70 P5 (ZYYX Print Finished!)\nM72 P1 ; play Ta-Da song\n"}, "machine_gcode_flavor": {"default_value": "Makerbot"}, "machine_head_with_fans_polygon": {"default_value": [[-37, 50], [25, 50], [25, -40], [-37, -40]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 195}, "machine_name": {"default_value": "ZYYX Pro"}, "machine_start_gcode": {"default_value": "; ZYYX Pro start gcode\n; Author <PERSON> 2024\nM73 P0; enable build progress\nM420 P20; set back fan speed 10 off 11-20 10-100%\nM140 S10 T0; set 100% heater power\nM140 S99 T0; set chamber heater negative hysteresis 19 degrees\nM140 S102 T0; set chamber heater positive hysteresis 2 degrees\nM140 S{material_bed_temperature_layer_0} T0; set chamber temperature\nM104 S{material_print_temperature_layer_0} T0; set nozzle temperature\nG21; set units to mm\nG90; set positioning to absolute\nG130 X80 Y90 A127 B127 ; Set Stepper Vref to default value\n\n; Home xy-axis\nG162 X Y F2500; home XY axes maximum\nG92 X0 Y0\nG1 X-5 Y-5 F2500\nG162 X Y F200; home XY axes maximum slowly\nG92 X135 Y114\n\n; Home z-axis\nG161 Z F1100; home Z axis minimum\nG92 Z0\nG1 Z2 F1100\nG161 Z F100; home Z axis minimum slowly\nG92 Z0\nM132 Z; Recall home offsets for Z\n\n; Calibrate point 1 (we're already at point 1)\nM131 A; store surface calibration point 1\nG1 Z2 F1100; back up from buildplate\n\n; Calibrate point 2\nG1 X-47 F7000; move to 2nd probing point\nG161 Z F100\nM131 B; store surface calibration point 1\nG1 Z2 F1100; back up from buildplate\n\n; Calibrate point 3\nG1 X135 Y-114 F7000; move to 2nd probing point\nG161 Z F100\nM131 AB; store surface calibration point 3\nG1 Z2 F1100; back up from buildplate\nM132 AB; Activate auto-leveling\n\n; Extrude material over hole\nM133 T0; stabilize extruder temperature\nM126 S{cool_fan_speed_0}; Activate fan\nG4 P1000; Wait a little bit longer\nG1 Z0.10 E500 F50\nG1 X115 Y-95 F1000\nG92 E0 ; Set E to 0\n; End of start gcode"}, "machine_steps_per_mm_e": {"default_value": 96.27520187033366}, "machine_steps_per_mm_x": {"default_value": 88.888889}, "machine_steps_per_mm_y": {"default_value": 88.888889}, "machine_steps_per_mm_z": {"default_value": 400}, "machine_width": {"default_value": 265}, "material_diameter": {"default_value": 1.75}}}