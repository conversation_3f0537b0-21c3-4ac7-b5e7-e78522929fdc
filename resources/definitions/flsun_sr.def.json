{"version": 2, "name": "Flsun Super Racer", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Flsun", "file_formats": "text/x-gcode", "platform": "flsun_sr.stl", "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "flsun_sr_extruder_0"}, "platform_offset": [0, -81, -43.5], "preferred_quality_type": "normal"}, "overrides": {"gantry_height": {"value": "0"}, "infill_sparse_density": {"default_value": 15}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 264}, "machine_end_gcode": {"default_value": "G91 ; relative coordinates\nG1 E-1 F300 ; retract filament a bit before lifting\nG1 Z+5 E-5 F6000 ; raise platform from current position\nG28 X0 Y0 ; home axis\nG90 ; absolute coordinates\nG92 E0 ; reset extruder\nM104 S0 ; turn off hotend\nM140 S0 ; turn off heat bed\nM107 ; turn off fans\nM84 ; disable motors\n"}, "machine_extruder_count": {"default_value": 1}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[0, 0], [0, 0], [0, 0], [0, 0]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 320}, "machine_nozzle_size": {"default_value": 0.4}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": "G21 ; millimeter units\nG90 ; absolute coordinates\nM82 ; E absolute\nM140 S{material_bed_temperature_layer_0}\nM104 S{material_print_temperature_layer_0}\nM190 S{material_bed_temperature_layer_0}\nM109 S{material_initial_print_temperature}\nG28 ; home axis\nM420 S1 ; enable mesh leveling\n; Lower nozzle and move to start position\nG1 Z150\nG1 X-130 Y0 Z0.4 F3000\n; Extrude about 40 mm by printing a 90 degree arc\nG3 X0 Y-130 I130 Z0.3 E40 F2700\n; Retract and move nozzle up\nG92 E0\nG1 E-1.5 F1800\nG0 Z0.5\nG1 E0 F300\n"}, "machine_width": {"default_value": 264}, "material_diameter": {"default_value": 1.75}, "retraction_enable": {"default_value": true}, "z_seam_type": {"value": "'back'"}}}