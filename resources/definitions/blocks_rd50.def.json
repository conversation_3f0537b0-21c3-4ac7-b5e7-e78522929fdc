{"version": 2, "name": "Blocks RD50", "inherits": "blocks_base", "metadata": {"visible": true, "platform": "blocks_rd50_platform.stl", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "blocks_rd50_extruder_0", "1": "blocks_rd50_extruder_1"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal", "preferred_variant_name": "ST - 0.4mm", "quality_definition": "blocks_base", "variants_name": "Print Core"}, "overrides": {"machine_depth": {"default_value": 500}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off\nM140 S0 ;heated bed heater off (if you have it)\nG91 ;relative positioning\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 X-20 Y-20 F6000 ;move Z up a bit and retract filament even more\nG28 X0 Y0 ;move X/Y to min endstops, so the head is out of the way\nM84 ;steppers off\nG90 ;absolute positioning\n"}, "machine_extruder_count": {"default_value": 2}, "machine_height": {"default_value": 500}, "machine_name": {"default_value": "Blocks RD50"}, "machine_start_gcode": {"default_value": "G21\nG90 ;absolute positioning\nG28 X0 Y0  ;move X/Y to min endstops\nG28 Z0 ;move Z to min endstops\n;PREHEAT\nM140 S{material_bed_temperature_layer_0}   ; Set Heat Bed temperature\nM104 S{material_print_temperature_layer_0} ; Set Extruder temperature\nG1 X-60 Y0 F6000\nG92 E0       ;zero the extruded length\nM190 S{material_bed_temperature_layer_0}   ; Wait for Heat Bed temperature\nM109 S{material_print_temperature_layer_0} ; Wait for Extruder temperature\nG1 F600 E20 ;extrude 10mm of feed stock\nG1 F200 E80 ;extrude 10mm of feed stock\nG12\nG92 E0       ;zero the extruded length again\nG29\nG1 Z0.2 F6000\nG1 F6000\n"}, "machine_width": {"default_value": 500}, "retraction_retract_speed": {"value": 50}}}