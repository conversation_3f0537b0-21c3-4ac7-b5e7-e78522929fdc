{"version": 2, "name": "Fusion3 F410", "inherits": "fusion3", "metadata": {"visible": true, "quality_definition": "fusion3"}, "overrides": {"acceleration_print": {"value": "machine_acceleration"}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": "machine_acceleration"}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "bottom_layers": {"value": "math.ceil(round(bottom_thickness / resolveOrValue('layer_height'), 4))"}, "brim_replaces_support": {"value": false}, "cool_fan_full_at_height": {"value": "layer_height_0 * 4"}, "cool_min_speed": {"value": "speed_print * 0.3"}, "gantry_height": {"value": 40}, "infill_before_walls": {"value": false}, "infill_overlap": {"value": 20.0}, "infill_pattern": {"value": "'zigzag' if infill_sparse_density > 89 else 'lines' if infill_sparse_density > 31 else 'cubic'"}, "infill_wipe_dist": {"value": 0.0}, "jerk_print": {"value": 10}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 2000}, "machine_depth": {"default_value": 355}, "machine_end_gcode": {"default_value": "; Fusion3 F410 end code\nG91  ;relative positioning\nG1 E-5.00 F1000  ;retract 5mm of filament\nG1 Z+1.00 X-20.0 Y+20.0 F10000  ;short quick move to disengage from print\nG90  ;absolute positioning\nG1 X0.0 Y350.0 F9000  ; move head to back left corner\nG91  ; relative positioning\nG1 E-5.00 F500  ;retract additional filament to prevent oozing\nG90 ;absolute positioning\nM104 S0  ;turn off hotend\nM140 S0  ;turn off heatbed\n; Reset filament monitor\nM42 P63 S0\nG4 P100\nG4 P50\nM42 P63 S1\nG1 Z315 F1000 ;move print bed down to idle position\nM106 S0  ; shut off blower\nM84  ;motors off\nM561  ;clear bed probe transformation\n"}, "machine_gcode_flavor": {"default_value": "RepRap (RepRap)"}, "machine_head_with_fans_polygon": {"default_value": [[-30, 30], [-30, -30], [30, -30], [30, 30]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 315}, "machine_max_acceleration_x": {"value": 5000}, "machine_max_acceleration_y": {"value": 5000}, "machine_max_acceleration_z": {"value": 500}, "machine_max_feedrate_e": {"value": 166}, "machine_max_feedrate_x": {"value": 550}, "machine_max_feedrate_y": {"value": 550}, "machine_max_feedrate_z": {"value": 40}, "machine_max_jerk_e": {"value": 30}, "machine_max_jerk_z": {"value": 1}, "machine_name": {"default_value": "Fusion3 F410"}, "machine_start_gcode": {"default_value": "; Fusion3 F410 start code\nM104 S0 ; shutdown heater and prepare to print\nG21  ; set units to mm\nG90  ; use absolute coordinates\nT0  ; select tool 0\nG92 E0.0  ; reset e count\nM220 S100 ; reset speed multiplier\nM140 S{material_bed_temperature_layer_0}  ; set bed temp and do not wait\nG32  ; call to run bed.g\nM190 S{material_bed_temperature_layer_0} ; Set bed temperature and wait\nM109 S{material_print_temperature_layer_0} ; set print head temperature and wait\n; === pause for heating ===\n; reset filament monitor\nM42 P63 S0\nG4 P100\nG4 P50\nM42 P63 S1\nG1 X350 Y0\nG1 Z10.0 F2000  ; move up\nG1 E10.0 F500  ; prime extruder\nG92 E0.0  ; reset e count\nG1 X330 Y15 F9000  ; move back\nG1 Z{layer_height_0} F1000  ; move down to begin wipe\nG1 X150 E4.0 F6000  ; wipe print head (extrude material while we do this)\nG92 E0.0  ; reset e count\n"}, "machine_width": {"default_value": 355}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "2 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": "True"}, "retract_at_layer_change": {"value": true}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 5}, "retraction_hop": {"value": 0.2}, "retraction_hop_enabled": {"value": true}, "retraction_prime_speed": {"maximum_value": "machine_max_feedrate_e", "maximum_value_warning": "machine_max_feedrate_e *0.75"}, "retraction_retract_speed": {"maximum_value": "machine_max_feedrate_e", "maximum_value_warning": "machine_max_feedrate_e *0.75"}, "retraction_speed": {"default_value": 100, "maximum_value": "machine_max_feedrate_e", "maximum_value_warning": "machine_max_feedrate_e *0.75"}, "skin_overlap": {"value": 10.0}, "skirt_gap": {"value": 10.0}, "skirt_line_count": {"value": 3}, "speed_infill": {"value": "speed_print *0.7"}, "speed_layer_0": {"value": "speed_print *0.4"}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": 100}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_travel": {"value": "speed_print * 1.5"}, "speed_travel_layer_0": {"value": "speed_travel *0.5"}, "speed_wall": {"value": "speed_print *0.6"}, "speed_wall_x": {"value": "speed_print *0.85"}, "speed_z_hop": {"value": 5}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width/2.0/layer_height)))"}, "support_brim_enable": {"value": true}, "support_brim_width": {"value": 4}, "support_infill_angles": {"value": [45]}, "support_infill_rate": {"value": "0 if support_enable and support_structure == 'tree' else 30"}, "support_interface_density": {"value": 33.333}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"value": "'grid'"}, "support_pattern": {"value": "'zigzag'"}, "support_wall_count": {"value": 0}, "support_xy_distance": {"value": "wall_line_width_0 * 3"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height*2"}, "top_bottom_pattern": {"value": "'zigzag'"}, "top_bottom_pattern_0": {"value": "'zigzag'"}, "top_bottom_thickness": {"minimum_value_warning": "0.4", "value": "(layer_height*3) + layer_height_0"}, "top_layers": {"value": "math.ceil(round(top_thickness / resolveOrValue('layer_height'), 4))"}, "travel_avoid_other_parts": {"value": false}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0.0}, "z_seam_corner": {"value": "'z_seam_corner_inner'"}, "z_seam_type": {"value": "'sharpest_corner'"}}}