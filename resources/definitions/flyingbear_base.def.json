{"version": 2, "name": "Flying Bear Base Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "oducc<PERSON>", "manufacturer": "Flying Bear", "file_formats": "text/x-gcode", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "imade3d_petg", "imade3d_pla", "innofill_innoflex60", "verbatim_bvoh"], "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "flyingbear_base_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal", "preferred_variant_name": "0.4mm Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "brim_gap": {"value": "line_width / 2 if line_width < 0.4 else 0.2"}, "brim_replaces_support": {"value": false}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "fill_outline_gaps": {"value": false}, "infill_before_walls": {"value": false}, "infill_enable_travel_optimization": {"value": true}, "infill_line_width": {"value": "line_width + 0.1"}, "infill_overlap": {"value": 30}, "infill_pattern": {"value": "'lines' if infill_sparse_density > 50 else 'cubic'"}, "infill_wipe_dist": {"value": 0.0}, "layer_height_0": {"value": 0.2}, "machine_buildplate_type": {"value": "'glass'"}, "machine_center_is_zero": {"default_value": false}, "machine_end_gcode": {"default_value": "G91 ;use relative coordinates\nG1 E-4 F1500 ;retract the filament\nG1 X5 Y5 Z0.2 F5000 ;wipe\nG1 Z5 F1500 ;raise z\nG90 ;use absolute coordinates\nG1 X10 Y{machine_depth} F5000 ;park print head\n\nM107 ;turn off fan\nM104 S0 ;turn off hotend\nM140 S0 ;turn off heatbed\nM84 ;disable motors"}, "machine_heated_bed": {"default_value": true}, "machine_name": {"default_value": "Flying Bear Base Printer"}, "machine_shape": {"default_value": "rectangular"}, "machine_start_gcode": {"default_value": "M220 S100 ;reset feedrate\nM221 S100 ;reset flowrate\nG90 ;use absolute coordinates\nM82 ;absolute extrusion mode\nG28 ;home\nG1 Z2 F1500 ;raise z\nG92 E0 ;reset extruder\n\nG1 X75 Y5 F5000 ;start position\nG1 Z0.28 F1500 ;lower z\nG1 E4 F500 ;prime the filament\nG1 X180 E10 F500 ;1st line\nG1 Y5.4 F5000\nG1 X75 E20 F500 ;2nd line\nG1 Z2 F1500 ;raise z\nG92 E0 ;reset extruder"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "meshfix_maximum_resolution": {"value": 0.25}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "5 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": true}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 10}, "retraction_hop": {"value": 0.2}, "retraction_hop_enabled": {"value": false}, "retraction_min_travel": {"value": 1.5}, "skin_line_width": {"value": "machine_nozzle_size"}, "skin_overlap": {"value": 10}, "skirt_brim_line_width": {"value": "line_width + 0.1"}, "skirt_brim_minimal_length": {"value": 50}, "skirt_brim_speed": {"value": "speed_layer_0"}, "skirt_gap": {"value": 10}, "skirt_line_count": {"value": 3}, "speed_infill": {"value": "speed_print * 1.5"}, "speed_layer_0": {"value": "speed_print / 2"}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": 60}, "speed_roofing": {"value": "speed_topbottom"}, "speed_support": {"value": "speed_print"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_travel": {"value": "150.0 if speed_print < 60 else 250.0 if speed_print > 100 else speed_print * 2.5"}, "speed_travel_layer_0": {"value": "100 if speed_layer_0 < 20 else 150 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_wall_x": {"value": "speed_print"}, "speed_z_hop": {"value": 5}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width/2.0/layer_height)))"}, "support_brim_enable": {"value": true}, "support_brim_width": {"value": 4}, "support_infill_rate": {"value": "0 if support_enable and support_structure == 'tree' else 20"}, "support_interface_density": {"value": 33}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_line_width": {"value": "line_width - 0.1"}, "support_interface_pattern": {"value": "'grid'"}, "support_pattern": {"value": "'zigzag'"}, "support_wall_count": {"value": 1}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height*2"}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3 if layer_height > 0.15 else 0.8"}, "travel_avoid_other_parts": {"value": true}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0.0}, "wall_thickness": {"value": "line_width * 3"}, "z_seam_corner": {"value": "'z_seam_corner_weighted'"}, "z_seam_type": {"value": "'sharpest_corner'"}}}