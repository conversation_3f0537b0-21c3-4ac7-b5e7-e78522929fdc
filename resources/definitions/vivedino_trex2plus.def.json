{"version": 2, "name": "Vivedino T-REX 2+", "inherits": "vivedino_base", "metadata": {"visible": true, "platform": "vivedino_trex.stl", "machine_extruder_trains": {"0": "trex2_extruder_0", "1": "trex2_extruder_1"}, "quality_definition": "vivedino_trex2plus"}, "overrides": {"gantry_height": {"value": 30}, "machine_depth": {"default_value": 400}, "machine_end_gcode": {"default_value": "G28 X0 Y0\nM104 S0 T1 ; turn off extruder\nM104 S0 T0\nM140 S0 ; turn off bed\nG28 X0\nM106 P0 S0\nM106 P1 S0\nM84 S0\nM84 XYE; disable motors except Z"}, "machine_extruder_count": {"default_value": 2}, "machine_head_with_fans_polygon": {"default_value": [[-30, 34], [-30, -32], [30, -32], [30, 34]]}, "machine_heat_zone_length": {"value": "10"}, "machine_height": {"default_value": 500}, "machine_start_gcode": {"default_value": "T0\nG28 ; home all axes\nM420 S1\nG1 X-42 F8000\nG92 E0\nG1 E5 F500\nG1 X0 F5000\nG1 X-40\nG1 X0\nG1 X-40\nG1 X0\nG1 X-40\nG1 X200\nG1 Y200 F5000"}, "machine_width": {"default_value": 400}, "retraction_amount": {"value": "2"}}}