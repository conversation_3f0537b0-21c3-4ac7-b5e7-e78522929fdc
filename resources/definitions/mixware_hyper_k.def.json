{"version": 2, "name": "Hyper K", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Mixware", "manufacturer": "Mixware", "file_formats": "text/x-gcode", "platform": "mixware_hyper_k_platform.stl", "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "mixware_hyper_k_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality": "coarse"}, "overrides": {"acceleration_print": {"value": 500}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": 500}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "brim_replaces_support": {"value": false}, "brim_width": {"default_value": 3}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "fill_outline_gaps": {"value": false}, "gantry_height": {"value": 25}, "infill_before_walls": {"value": false}, "infill_overlap": {"value": 30.0}, "infill_pattern": {"value": "'lines' if infill_sparse_density > 50 else 'cubic'"}, "infill_wipe_dist": {"value": 0.0}, "ironing_line_spacing": {"default_value": 0.4}, "ironing_pattern": {"default_value": "concentric"}, "jerk_print": {"value": 8}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 500}, "machine_depth": {"default_value": 225}, "machine_end_gcode": {"default_value": "G91; relative positioning\nG1 Z1.0 F3000 ; move z up little to prevent scratching of print\nG90; absolute positioning\nG1 X0 Y200 F1000 ; prepare for part removal\nM104 S0; turn off extruder\nM140 S0 ; turn off bed\nG1 X0 Y220 F1000 ; prepare for part removal\nM84 ; disable motors\nM106 S0 ; turn off fan"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 250}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 500}, "machine_max_acceleration_y": {"value": 500}, "machine_max_acceleration_z": {"value": 100}, "machine_max_feedrate_e": {"value": 50}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 10}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.4}, "machine_name": {"default_value": "Hyper K"}, "machine_start_gcode": {"default_value": "M140 S{material_bed_temperature} ; Heat bed\nM109 S{material_print_temperature} ; Heat nozzle\nM190 S{material_bed_temperature} ; Wait for bed heating\nG28 ; home all axes\nM117 Purge extruder\nG92 E0 ; reset extruder\nG1 Z5.0 F1000 ; move z up little to prevent scratching of surface\nG1 X0.1 Y20 Z0.3 F5000.0 ; move to start-line position\nG1 X0.1 Y100.0 Z0.3 F1500.0 E15 ; draw 1st line\nG1 X0.4 Y100.0 Z0.3 F5000.0 ; move to side a little\nG1 X0.4 Y20 Z0.3 F1500.0 E30 ; draw 2nd line\nG92 E0 ; reset extruder\nG1 Z5.0 F1000 ; move z up little to prevent scratching of surface"}, "machine_width": {"default_value": 225}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "2 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": "True"}, "raft_airgap": {"default_value": 0.24}, "raft_margin": {"default_value": 3, "minimum_value_warning": "0.01"}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 10}, "retraction_hop": {"value": 0.2}, "retraction_prime_speed": {"maximum_value": 200}, "retraction_retract_speed": {"maximum_value": 200}, "retraction_speed": {"maximum_value": 200}, "skin_overlap": {"value": 10.0}, "skirt_gap": {"value": 10.0}, "skirt_line_count": {"value": 3}, "speed_layer_0": {"value": 20.0}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": 50.0}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_travel": {"value": "120.0 if speed_print < 60 else 240.0 if speed_print > 100 else speed_print * 2.5"}, "speed_travel_layer_0": {"value": "60 if speed_layer_0 < 20 else 150 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_z_hop": {"value": 5}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width/2.0/layer_height)))"}, "support_brim_enable": {"value": true}, "support_brim_width": {"value": 4}, "support_enable": {"default_value": true}, "support_infill_rate": {"value": "0 if support_enable and support_structure == 'tree' else 15"}, "support_interface_density": {"value": 33.333}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"value": "'grid'"}, "support_type": {"default_value": "buildplate"}, "support_use_towers": {"value": false}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height * 2"}, "top_bottom_pattern": {"default_value": "zigzag"}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3"}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0.0}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_corner": {"value": "'z_seam_corner_weighted'"}, "z_seam_type": {"value": "'back'"}}}