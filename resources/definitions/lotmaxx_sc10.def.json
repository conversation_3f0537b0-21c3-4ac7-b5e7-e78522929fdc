{"version": 2, "name": "Lotmaxx SC-10", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "lotmaxx.com", "manufacturer": "Lotmaxx", "file_formats": "text/x-gcode", "platform": "lotmaxx_sc_10_20_platform.3mf", "has_materials": true, "machine_extruder_trains": {"0": "lotmaxx_sc10_extruder_0"}}, "overrides": {"acceleration_enabled": {"value": false}, "adhesion_type": {"value": "'none' if support_enable else 'skirt'"}, "cool_fan_enabled": {"value": true}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "gantry_height": {"value": 40}, "jerk_print": {"value": 8}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_depth": {"default_value": 235}, "machine_end_gcode": {"default_value": "; SC-10 Custom End G-code\nG4 ; Wait\nM220 S100 ; Reset Speed factor override percentage to default (100%)\nM221 S100 ; Reset Extrude factor override percentage to default (100%)\nG91 ; Set coordinates to relative\nG1 F1800 E-3 ; Retract filament 3 mm to prevent oozing\nG1 F3000 Z20 ; Move Z Axis up 20 mm to allow filament ooze freely\nG90 ; Set coordinates to absolute\nG1 X0 Y{machine_depth} F1000 ; Move Heat Bed to the front for easy print removal\nM84 ; Disable stepper motors\n; End of custom end GCode"}, "machine_head_with_fans_polygon": {"default_value": [[-32, 11], [-32, -32], [28, -32], [28, 11]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 320}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.4}, "machine_name": {"default_value": "Lotmaxx SC-10"}, "machine_start_gcode": {"default_value": "; SC-10 Custom Start G-code\nG28 ; Home all axes\nG92 E0 ; Reset Extruder\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X0.1 Y20 Z0.3 F5000.0 ; Move to start position\nG1 X0.1 Y200.0 Z0.3 F1500.0 E15 ; Draw the first line\nG1 X0.4 Y200.0 Z0.3 F5000.0 ; Move to side a little\nG1 X0.4 Y20 Z0.3 F1500.0 E30 ; Draw the second line\nG92 E0 ; Reset Extruder\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\n; End of custom start GCode"}, "machine_width": {"default_value": 235}, "material_diameter": {"default_value": 1.75}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "retraction_count_max": {"value": 100}, "retraction_enable": {"value": true}, "retraction_extrusion_window": {"value": 10}, "retraction_min_travel": {"value": 1.5}, "skirt_brim_speed": {"value": "speed_layer_0"}, "skirt_gap": {"value": 5.0}, "skirt_line_count": {"value": 4}, "speed_infill": {"value": "speed_print * 2"}, "speed_layer_0": {"value": 20.0}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": 60.0}, "speed_roofing": {"value": "speed_topbottom"}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_travel": {"value": "150.0 if speed_print < 60 else 250.0 if speed_print > 100 else speed_print * 2.5"}, "speed_travel_layer_0": {"value": "100 if speed_layer_0 < 20 else 150 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_wall_0": {"value": "speed_wall"}, "speed_wall_x": {"value": "speed_wall"}}}