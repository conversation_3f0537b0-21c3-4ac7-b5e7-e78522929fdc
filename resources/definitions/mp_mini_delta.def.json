{"version": 2, "name": "MP Mini Delta", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "MPMD V1 Facebook Group", "manufacturer": "Monoprice", "file_formats": "text/x-gcode", "platform": "mp_mini_delta_platform.3mf", "has_machine_materials": false, "has_machine_quality": false, "has_materials": true, "has_variant_materials": false, "has_variants": false, "machine_extruder_trains": {"0": "mp_mini_delta_extruder_0"}, "platform_offset": [0, 0, 0], "preferred_quality_type": "normal", "supports_usb_connection": true}, "overrides": {"coasting_enable": {"default_value": true}, "layer_height": {"default_value": 0.14}, "layer_height_0": {"default_value": 0.21}, "line_width": {"value": "round(machine_nozzle_size, 2)"}, "machine_acceleration": {"default_value": 3000}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 110}, "machine_end_gcode": {"default_value": "M107; \nM104 S0; turn off hotend heater \nM140 S0; turn off bed heater \nG91; Switch to use Relative Coordinates \nG1 E-2 F300; retract the filament a bit before lifting the nozzle to release some of the pressure \nG1 Z5 E-5 F4800; move nozzle up a bit and retract filament even more \nG28 X0; return to home positions so the nozzle is out of the way \nM84; turn off stepper motors \nG90; switch to absolute positioning \nM82; absolute extrusion mode"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 120}, "machine_max_acceleration_e": {"default_value": 10000}, "machine_max_acceleration_x": {"default_value": 800}, "machine_max_acceleration_y": {"default_value": 800}, "machine_max_acceleration_z": {"default_value": 800}, "machine_max_feedrate_e": {"default_value": 50}, "machine_max_feedrate_x": {"default_value": 150}, "machine_max_feedrate_y": {"default_value": 150}, "machine_max_feedrate_z": {"default_value": 150}, "machine_max_jerk_e": {"default_value": 5}, "machine_max_jerk_xy": {"default_value": 20}, "machine_max_jerk_z": {"default_value": 20}, "machine_nozzle_size": {"default_value": 0.4}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": ";MPMD V1 Basic Calibration Tutorial: \n; https://www.thingiverse.com/thing:3892011 \n; \n; If you want to put calibration values in your \n; Start Gcode, put them here. \n; \n;If on stock firmware, at minimum, consider adding \n;M665 R here since there is a firmware bug.  \n; \n; Calibration part ends here \n; \nG90 ; switch to absolute positioning \nG92 E0 ; reset extrusion distance \nG1 E20 F200 ; purge 20mm of filament to prime nozzle. \nG92 E0 ; reset extrusion distance \nG4 S5 ; Pause for 5 seconds to allow time for removing extruded filament \nG28 ; start from home position \nG1 E-6 F900 ; retract 6mm of filament before starting the bed leveling process \nG92 E0 ; reset extrusion distance \nG4 S5 ; pause for 5 seconds to allow time for removing extruded filament \nG29 P2 Z0.28 ; Auto-level ; ADJUST Z higher or lower to set first layer height. Start with 0.02 adjustments. \nG1 Z30 ; raise Z 30mm to prepare for priming the nozzle \nG1 E5 F200 ; extrude 5mm of filament to help prime the nozzle just prior to the start of the print \nG92 E0 ; reset extrusion distance \nG4 S5 ; pause for 5 seconds to allow time for cleaning the nozzle and build plate if needed "}, "machine_width": {"default_value": 110}, "material_bed_temperature": {"value": 40}, "material_bed_temperature_layer_0": {"value": "material_bed_temperature"}, "retract_at_layer_change": {"default_value": true}, "retraction_amount": {"default_value": 4}, "retraction_hop_enabled": {"default_value": false}, "retraction_speed": {"default_value": 50}}}