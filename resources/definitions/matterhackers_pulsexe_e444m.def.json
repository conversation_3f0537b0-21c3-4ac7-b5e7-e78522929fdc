{"version": 2, "name": "Pulse XE E-444M", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Zwitch Guitars", "manufacturer": "MatterHackers", "file_formats": "text/x-gcode", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": false, "has_materials": true, "has_variants": false, "machine_extruder_trains": {"0": "matterhackers_extruder"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal"}, "overrides": {"adhesion_type": {"value": "skirt"}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "gantry_height": {"value": 23}, "machine_acceleration": {"value": 1300}, "machine_depth": {"default_value": 220}, "machine_end_gcode": {"default_value": "M104 S0 ; turn off extruder\nM140 S0 ; turn off heatbed\nM107 ; turn off fan\nG1 X0 Y{machine_depth}; home X axis and push Y forward\nG28 Z0\nM84 ; disable motors"}, "machine_gcode_flavor": {"default_value": "<PERSON><PERSON>"}, "machine_head_with_fans_polygon": {"default_value": [[-28, 45], [-28, -18], [40, 45], [40, -18]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 215}, "machine_max_feedrate_e": {"value": 75}, "machine_max_feedrate_x": {"value": 300}, "machine_max_feedrate_y": {"value": 300}, "machine_max_feedrate_z": {"value": 30}, "machine_name": {"default_value": "Pulse XE E-444M"}, "machine_start_gcode": {"default_value": "G21 ; set units to millimeters\nG90 ; use absolute positioning\nM82 ; absolute extrusion mode\nG28 ; home axes\nM104 S{material_print_temperature_layer_0} ; set extruder temp\nM140 S{material_bed_temperature_layer_0} ; set bed temp\nM190 S{material_bed_temperature_layer_0} ; wait for bed temp\nM109 S{material_print_temperature_layer_0} ; wait for extruder temp\nG29 ; mesh bed leveling\n\nG92 E0\nG1 X5 Y5 Z0.8 F1800\nG1 X100 Z0.3 E25 F900\nG92 E0\nG1 E-2 F2400"}, "machine_steps_per_mm_e": {"value": 415}, "machine_steps_per_mm_x": {"value": 80}, "machine_steps_per_mm_y": {"value": 80}, "machine_steps_per_mm_z": {"value": 400}, "machine_width": {"default_value": 250}, "material_diameter": {"value": 1.75}, "optimize_wall_printing_order": {"value": true}, "speed_layer_0": {"value": 20.0}, "speed_print": {"value": 50}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3"}, "travel_retract_before_outer_wall": {"value": true}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_type": {"value": "back"}}}