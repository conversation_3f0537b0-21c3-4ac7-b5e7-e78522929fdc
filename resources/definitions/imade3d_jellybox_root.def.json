{"version": 2, "name": "imade3d_jellybox_root", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "Imade3D", "manufacturer": "Imade3D", "file_formats": "text/x-gcode", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "generic_abs", "generic_cpe", "generic_hips", "generic_nylon", "generic_pc", "generic_petg", "generic_pla", "generic_pva", "innofill_innoflex60", "verbatim_bvoh"]}, "overrides": {"machine_center_is_zero": {"default_value": false}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "material_bed_temperature": {"minimum_value": "0"}, "material_diameter": {"default_value": 1.75}, "material_print_temperature": {"minimum_value": "0"}, "material_standby_temperature": {"minimum_value": "0"}, "relative_extrusion": {"enabled": true, "value": true}}}