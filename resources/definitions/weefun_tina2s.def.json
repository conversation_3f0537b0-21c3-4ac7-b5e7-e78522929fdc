{"version": 2, "name": "WEEFUN TINA2S", "inherits": "weedo_base", "metadata": {"visible": true, "author": "WEEFUN", "manufacturer": "WEEFUN", "file_formats": "text/x-gcode", "exclude_materials": ["3D-Fuel_PLA_PRO_Black", "3D-Fuel_PLA_SnapSupport", "bestfilament_abs_skyblue", "bestfilament_petg_orange", "bestfilament_pla_green", "leapfrog_abs_natural", "leapfrog_epla_natural", "leapfrog_pva_natural", "generic_abs_175", "generic_asa_175", "generic_cpe_175", "generic_nylon_175", "generic_pc_175", "generic_petg_175", "generic_pva_175", "goofoo_abs", "goofoo_asa", "goofoo_bronze_pla", "goofoo_emarble_pla", "goofoo_esilk_pla", "goofoo_hips", "goofoo_pa_cf", "goofoo_pa", "goofoo_pc", "goofoo_peek", "goofoo_petg", "goofoo_pla", "goofoo_pva", "goofoo_tpe_83a", "goofoo_tpu_87a", "goofoo_tpu_95a", "goofoo_wood_pla", "emotiontech_abs", "emotiontech_absx", "emotiontech_acetate", "emotiontech_asax", "emotiontech_bvoh", "emotiontech_copa", "emotiontech_hips", "emotiontech_nylon_1030", "emotiontech_nylon_1030cf", "emotiontech_nylon_1070", "emotiontech_pc", "emotiontech_pekk", "emotiontech_petg", "emotiontech_pla", "emotiontech_pla_hr_870", "emotiontech_pva-m", "emotiontech_pva-s", "emotiontech_tpu98a", "eryone_petg", "eryone_pla_glow", "eryone_pla_matte", "eryone_pla_wood", "eryone_pla", "eryone_tpu", "eSUN_PETG_Black", "eSUN_PETG_Grey", "eSUN_PETG_Purple", "eSUN_PLA_PRO_Black", "eSUN_PLA_PRO_Grey", "eSUN_PLA_PRO_Purple", "eSUN_PLA_PRO_White", "Extrudr_GreenTECPro_Anthracite_175", "Extrudr_GreenTECPro_Black_175", "Extrudr_GreenTECPro_Blue_175", "Extrudr_GreenTECPro_Nature_175", "Extrudr_GreenTECPro_Red_175", "Extrudr_GreenTECPro_Silver_175", "Extrudr_GreenTECPro_White_175", "verbatim_bvoh_175", "Vertex_Delta_ABS", "Vertex_Delta_PET", "Vertex_Delta_PLA", "Vertex_Delta_TPU", "chromatik_pla", "dsm_arnitel2045_175", "dsm_novamid1070_175", "fabtotum_abs", "fabtotum_nylon", "fabtotum_pla", "fabtotum_tpu", "fdplast_abs_tomato", "fdplast_petg_gray", "fdplast_pla_olive", "fiberlogy_hd_pla", "filo3d_pla", "filo3d_pla_green", "filo3d_pla_red", "imade3d_petg_green", "imade3d_petg_pink", "imade3d_pla_green", "imade3d_pla_pink", "imade3d_petg_175", "imade3d_pla_175", "innofill_innoflex60_175", "layer_one_black_pla", "layer_one_dark_gray_pla", "layer_one_white_pla", "octofiber_pla", "polyflex_pla", "polymax_pla", "polyplus_pla", "polywood_pla", "redd_abs", "redd_asa", "redd_hips", "redd_nylon", "redd_petg", "redd_pla", "redd_tpe", "tizyx_abs", "tizyx_flex", "tizyx_petg", "tizyx_pla_bois", "tizyx_pla", "tizyx_pva", "Vertex_Delta_ABS", "Vertex_Delta_PET", "Vertex_Delta_PLA_Glitter", "Vertex_Delta_PLA_Mat", "Vertex_Delta_PLA_Satin", "Vertex_Delta_PLA_Wood", "Vertex_Delta_PLA", "Vertex_Delta_TPU", "volumic_abs_ultra", "volumic_arma_ultra", "volumic_asa_ultra", "volumic_br80_ultra", "volumic_bumper_ultra", "volumic_cu80_ultra", "volumic_flex93_ultra", "volumic_medical_ultra", "volumic_nylon_ultra", "volumic_pekk_carbone", "volumic_petg_ultra", "volumic_petgcarbone_ultra", "volumic_pla_ultra", "volumic_pp_ultra", "volumic_strong_ultra", "volumic_support_ultra", "xyzprinting_abs", "xyzprinting_antibact_pla", "xyzprinting_carbon_fiber", "xyzprinting_colorinkjet_pla", "xyzprinting_flexible", "xyzprinting_metallic_pla", "xyzprinting_nylon", "xyzprinting_petg", "xyzprinting_pla", "xyzprinting_tough_pla", "xyzprinting_tpu", "zyyx_pro_flex", "zyyx_pro_pla"], "platform_offset": [0, 0, 0]}, "overrides": {"machine_depth": {"default_value": 110}, "machine_end_gcode": {"default_value": ";(**** end.gcode for tina2****)\nM203 Z15\nM104 S0\nM107\nG92 E0 (Reset after prime)\nG0 E-1 F300\nG28 Z F300\nG28 X0 Y0\nG1 Y90 F1000"}, "machine_height": {"default_value": 100}, "machine_name": {"default_value": "WEEFUN TINA2S"}, "machine_start_gcode": {"default_value": ";MachineType:{machine_name}\n;FilamentType:{material_type}\n;InfillDensity:{infill_sparse_density}\n;Extruder0Temperature:{material_print_temperature}\n;BedTemperature:{material_bed_temperature}\n\n;(**** start.gcode for tina2****)\nM203 Z15\nM104 S150\nG28 Z\nG28 X Y; Home extruder\nG1 X55 Y55 F1000\nG29\nM107 ; Turn off fan\nG90 ; Absolute positioning\nM82 ; Extruder in absolute mode\nM109 S{material_print_temperature_layer_0}\nG92 E0 ; Reset extruder position\nG1 X90 Y6 Z0.27 F2000\nG1 X20 Y6 Z0.27 E15 F1000\nG92 E0 ; Reset extruder position\nM203 Z5"}, "machine_width": {"default_value": 100}, "material_bed_temperature": {"maximum_value": "65", "maximum_value_warning": "60"}, "raft_base_thickness": {"value": 0.35}, "speed_infill": {"value": 40.0}, "speed_print": {"value": 40.0}, "speed_print_layer_0": {"value": 22.0}, "speed_roofing": {"value": 25.0}, "speed_support_bottom": {"value": 30.0}, "speed_support_infill": {"value": 45.0}, "speed_support_roof": {"value": 30.0}, "speed_topbottom": {"value": 30.0}, "speed_travel": {"value": 65.0}, "speed_travel_layer_0": {"value": 60}, "speed_wall": {"value": 25.0}, "speed_wall_0": {"value": 20.0}, "speed_wall_x": {"value": 25.0}}}