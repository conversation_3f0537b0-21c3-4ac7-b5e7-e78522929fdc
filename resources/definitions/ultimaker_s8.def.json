{"version": 2, "name": "UltiMaker S8", "inherits": "ultimaker_s5", "metadata": {"visible": true, "author": "UltiMaker", "manufacturer": "Ultimaker B.V.", "file_formats": "application/x-ufp;text/x-gcode", "platform": "ultimaker_s7_platform.obj", "bom_numbers": [10600], "exclude_materials": ["generic_hips", "generic_flexible", "generic_cffpps", "generic_cffpa", "generic_cffcpe", "generic_gffpa", "generic_gffcpe", "structur3d_", "ultimaker_ppscf"], "firmware_update_info": {"check_urls": ["https://software.ultimaker.com/releases/firmware/10600/stable/um-update.swu.version"], "id": 10600, "update_url": "https://ultimaker.com/firmware?utm_source=cura&utm_medium=software&utm_campaign=fw-update"}, "first_start_actions": ["DiscoverUM3Action"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "ultimaker_s8_extruder_left", "1": "ultimaker_s8_extruder_right"}, "nozzle_offsetting_for_disallowed_areas": false, "platform_offset": [0, 0, 0], "platform_texture": "UltimakerS8backplate.png", "preferred_material": "ultimaker_pla_blue", "preferred_quality_type": "draft", "preferred_variant_name": "AA+ 0.4", "supported_actions": ["DiscoverUM3Action"], "supports_material_export": true, "supports_network_connection": true, "supports_usb_connection": false, "variants_name": "Print Core", "variants_name_has_translation": true, "weight": -2}, "overrides": {"acceleration_flooring": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_roofing"}, "acceleration_infill": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_print"}, "acceleration_layer_0": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": 2000}, "acceleration_prime_tower": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_print"}, "acceleration_print": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": 20000}, "acceleration_print_layer_0": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_layer_0"}, "acceleration_roofing": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_wall_0"}, "acceleration_skirt_brim": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_layer_0"}, "acceleration_support": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_print"}, "acceleration_support_bottom": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_support_interface"}, "acceleration_support_infill": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_support"}, "acceleration_support_interface": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_support"}, "acceleration_support_roof": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_support_interface"}, "acceleration_topbottom": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_print"}, "acceleration_travel": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": 10000}, "acceleration_travel_enabled": {"value": true}, "acceleration_travel_layer_0": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_layer_0"}, "acceleration_wall": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_print/8"}, "acceleration_wall_0": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_wall"}, "acceleration_wall_0_flooring": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_wall_0_roofing"}, "acceleration_wall_0_roofing": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_wall_0"}, "acceleration_wall_x": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_print"}, "acceleration_wall_x_flooring": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_wall_x_roofing"}, "acceleration_wall_x_roofing": {"maximum_value": "machine_max_acceleration_x", "maximum_value_warning": "machine_max_acceleration_x*0.8", "value": "acceleration_wall"}, "adhesion_type": {"value": "'brim' if support_enable and support_structure=='tree' else 'skirt'"}, "bottom_thickness": {"value": "3*layer_height if top_layers==4 and not support_enable else top_bottom_thickness"}, "bridge_skin_material_flow": {"value": 200}, "bridge_skin_speed": {"unit": "mm/s", "value": "bridge_wall_speed"}, "bridge_sparse_infill_max_density": {"value": 50}, "bridge_wall_material_flow": {"value": "bridge_skin_material_flow"}, "bridge_wall_min_length": {"value": 10}, "bridge_wall_speed": {"unit": "mm/s", "value": 50}, "build_volume_temperature": {"force_depends_on_settings": ["support_extruder_nr", "support_enable"]}, "cool_during_extruder_switch": {"value": "'all_fans'"}, "cool_min_layer_time": {"value": 5}, "cool_min_layer_time_overhang": {"value": 9}, "cool_min_layer_time_overhang_min_segment_length": {"value": 2}, "cool_min_speed": {"value": 6}, "cool_min_temperature": {"minimum_value_warning": "material_print_temperature-15", "value": "material_print_temperature-15"}, "default_material_print_temperature": {"maximum_value_warning": 320}, "extra_infill_lines_to_support_skins": {"value": "'walls_and_lines'"}, "flooring_layer_count": {"value": 1}, "gradual_flow_enabled": {"value": false}, "hole_xy_offset": {"value": 0.075}, "infill_material_flow": {"value": "material_flow"}, "infill_overlap": {"value": 10}, "infill_pattern": {"value": "'zigzag' if infill_sparse_density > 80 else 'grid'"}, "infill_sparse_density": {"value": 15}, "infill_wall_line_count": {"value": "1 if infill_sparse_density > 80 else 0"}, "initial_bottom_layers": {"value": 2}, "jerk_flooring": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_roofing"}, "jerk_infill": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_print"}, "jerk_layer_0": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_print/2"}, "jerk_prime_tower": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_wall"}, "jerk_print": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "10000"}, "jerk_print_layer_0": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_layer_0"}, "jerk_roofing": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_wall_0"}, "jerk_skirt_brim": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_layer_0"}, "jerk_support": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_print"}, "jerk_support_bottom": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_support_interface"}, "jerk_support_infill": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_support"}, "jerk_support_interface": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_support"}, "jerk_support_roof": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_support_interface"}, "jerk_topbottom": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_print"}, "jerk_travel": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": 10000}, "jerk_travel_enabled": {"value": true}, "jerk_travel_layer_0": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_travel"}, "jerk_wall": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_print/5"}, "jerk_wall_0": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_wall"}, "jerk_wall_0_flooring": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_wall_0_roofing"}, "jerk_wall_0_roofing": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_wall_0"}, "jerk_wall_x": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_print"}, "jerk_wall_x_flooring": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_wall_x_roofing"}, "jerk_wall_x_roofing": {"maximum_value_warning": "machine_max_jerk_xy / 2", "unit": "m/s³", "value": "jerk_wall_0"}, "machine_gcode_flavor": {"default_value": "Cheetah"}, "machine_max_acceleration_x": {"default_value": 50000}, "machine_max_acceleration_y": {"default_value": 50000}, "machine_max_feedrate_x": {"default_value": 500}, "machine_max_feedrate_y": {"default_value": 500}, "machine_max_jerk_e": {"default_value": 20000, "unit": "m/s³", "value": "20000 if machine_gcode_flavor == 'Cheetah' else 100"}, "machine_max_jerk_xy": {"default_value": 1000000, "unit": "m/s³", "value": "1000000 if machine_gcode_flavor == 'Cheetah' else 500"}, "machine_max_jerk_z": {"default_value": 20000, "unit": "m/s³", "value": "20000 if machine_gcode_flavor == 'Cheetah' else 100"}, "machine_name": {"default_value": "Ultimaker S8"}, "machine_nozzle_cool_down_speed": {"default_value": 1.3}, "machine_nozzle_heat_up_speed": {"default_value": 0.6}, "machine_start_gcode": {"default_value": "M213 U0.1 ;undercut 0.1mm"}, "material_bed_temperature": {"force_depends_on_settings": ["support_extruder_nr", "support_enable"]}, "material_extrusion_cool_down_speed": {"value": 0}, "material_final_print_temperature": {"value": "material_print_temperature - 5"}, "material_initial_print_temperature": {"value": "material_print_temperature - 5"}, "material_pressure_advance_factor": {"enabled": true, "value": 0.5}, "material_print_temperature": {"maximum_value_warning": 320}, "material_print_temperature_layer_0": {"maximum_value_warning": 320}, "max_flow_acceleration": {"value": 8.0}, "max_skin_angle_for_expansion": {"value": 45}, "meshfix_maximum_resolution": {"value": 0.4}, "min_infill_area": {"default_value": 10}, "optimize_wall_printing_order": {"value": false}, "prime_tower_brim_enable": {"value": true}, "prime_tower_min_volume": {"value": 10}, "prime_tower_mode": {"resolve": "'normal'"}, "retraction_amount": {"value": 6.5}, "retraction_combing_avoid_distance": {"value": 1.2}, "retraction_combing_max_distance": {"value": 50}, "retraction_hop": {"value": 1}, "retraction_hop_after_extruder_switch_height": {"value": 2}, "retraction_hop_enabled": {"value": true}, "retraction_min_travel": {"value": "5 if support_enable and support_structure=='tree' else line_width * 2.5"}, "retraction_prime_speed": {"value": 15}, "skin_edge_support_thickness": {"value": 0}, "skin_material_flow": {"value": 95}, "skin_overlap": {"value": 0}, "skin_preshrink": {"value": 0}, "skirt_brim_minimal_length": {"value": 1000}, "skirt_brim_speed": {"maximum_value_warning": 300, "value": "speed_layer_0"}, "skirt_line_count": {"value": 5}, "small_skin_on_surface": {"value": false}, "small_skin_width": {"value": 4}, "speed_flooring": {"maximum_value_warning": 300, "value": "speed_roofing"}, "speed_infill": {"maximum_value_warning": 300, "value": "speed_print"}, "speed_ironing": {"maximum_value_warning": 300, "value": 20}, "speed_layer_0": {"maximum_value_warning": 300, "value": "min(speed_wall, 50)"}, "speed_prime_tower": {"maximum_value_warning": 300, "value": "min(speed_wall, 50)"}, "speed_print": {"maximum_value_warning": 300, "value": 150}, "speed_print_layer_0": {"maximum_value_warning": 300, "value": "speed_layer_0"}, "speed_roofing": {"maximum_value_warning": 300, "value": "speed_wall"}, "speed_support": {"maximum_value_warning": 300, "value": "speed_wall"}, "speed_support_bottom": {"maximum_value_warning": 300, "value": "speed_support_interface"}, "speed_support_infill": {"maximum_value_warning": 300, "value": "speed_support"}, "speed_support_interface": {"maximum_value_warning": 300, "value": 80}, "speed_support_roof": {"maximum_value_warning": 300, "value": "speed_support_interface"}, "speed_topbottom": {"maximum_value_warning": 300, "value": "speed_print"}, "speed_travel": {"maximum_value": 500, "maximum_value_warning": 500, "value": 500}, "speed_travel_layer_0": {"maximum_value": 500, "maximum_value_warning": 500, "value": 150}, "speed_wall": {"maximum_value_warning": 300, "value": "speed_print*2/3"}, "speed_wall_0": {"maximum_value_warning": 300, "value": "speed_wall"}, "speed_wall_0_flooring": {"maximum_value_warning": 300, "value": "speed_wall_0_roofing"}, "speed_wall_0_roofing": {"maximum_value_warning": 300, "value": "speed_wall"}, "speed_wall_x": {"maximum_value_warning": 300, "value": "speed_print"}, "speed_wall_x_flooring": {"maximum_value_warning": 300, "value": "speed_wall_x_roofing"}, "speed_wall_x_roofing": {"maximum_value_warning": 300, "value": "speed_wall"}, "support_angle": {"value": 60}, "support_bottom_distance": {"maximum_value_warning": "3*layer_height"}, "support_bottom_offset": {"value": 0}, "support_brim_width": {"value": 10}, "support_interface_enable": {"value": true}, "support_interface_offset": {"value": "support_offset"}, "support_line_width": {"value": "1.25*line_width"}, "support_offset": {"value": "1.2 if support_structure == 'tree' else 0.8"}, "support_pattern": {"value": "'gyroid' if support_structure == 'tree' else 'lines'"}, "support_roof_height": {"minimum_value_warning": 0}, "support_structure": {"value": "'normal'"}, "support_top_distance": {"maximum_value_warning": "3*layer_height"}, "support_tree_angle": {"value": 50}, "support_tree_angle_slow": {"value": 35}, "support_tree_bp_diameter": {"value": 15}, "support_tree_branch_diameter": {"value": 8}, "support_tree_tip_diameter": {"value": 1.0}, "support_tree_top_rate": {"value": 20}, "support_xy_distance_overhang": {"value": "machine_nozzle_size"}, "support_z_distance": {"value": "0.4*material_shrinkage_percentage_z/100.0"}, "top_bottom_thickness": {"value": "round(4*layer_height, 2)"}, "travel_avoid_other_parts": {"value": true}, "travel_avoid_supports": {"value": true}, "wall_0_acceleration": {"value": 1000}, "wall_0_deceleration": {"value": 1000}, "wall_0_end_speed_ratio": {"value": 100}, "wall_0_speed_split_distance": {"value": 0.2}, "wall_0_start_speed_ratio": {"value": 100}, "wall_0_wipe_dist": {"value": 0}, "wall_material_flow": {"value": 95}, "wall_overhang_angle": {"value": 45}, "wall_x_material_flow": {"value": 100}, "xy_offset": {"value": 0.05}, "z_seam_corner": {"value": "'z_seam_corner_weighted'"}, "z_seam_position": {"value": "'backright'"}, "z_seam_type": {"value": "'sharpest_corner'"}}}