{"version": 2, "name": "Ultimaker 2+", "inherits": "ultimaker2", "metadata": {"author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Ultimaker B.V.", "file_formats": "text/x-gcode", "platform": "ultimaker2_platform.obj", "exclude_materials": ["generic_bam", "generic_cffcpe", "generic_cffpa", "generic_flexible", "generic_gffcpe", "generic_gffpa", "generic_hips", "generic_petcf", "generic_petg", "generic_pva", "generic_tough_pla", "structur3d_", "ultimaker_bam", "ultimaker_petcf", "ultimaker_petg", "ultimaker_pva", "ultimaker_tough_pla"], "firmware_file": "MarlinUltimaker2plus.hex", "first_start_actions": [], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "ultimaker2_plus_extruder_0"}, "platform_texture": "Ultimaker2Plusbackplate.png", "preferred_variant_name": "0.4 mm", "supported_actions": [], "weight": 1}, "overrides": {"gantry_height": {"value": "52"}, "layer_height_0": {"value": "round(machine_nozzle_size / 1.5, 2)"}, "machine_disallowed_areas": {"default_value": [[[-115, 112.5], [-78, 112.5], [-80, 102.5], [-115, 102.5]], [[115, 112.5], [115, 102.5], [105, 102.5], [103, 112.5]], [[-115, -112.5], [-115, -104.5], [-84, -104.5], [-82, -112.5]], [[115, -112.5], [108, -112.5], [110, -104.5], [115, -104.5]]]}, "machine_head_with_fans_polygon": {"default_value": [[-44, 14], [-44, -34], [64, 14], [64, -34]]}, "machine_heat_zone_length": {"default_value": 20}, "machine_height": {"default_value": 205}, "machine_name": {"default_value": "Ultimaker 2+"}, "machine_show_variants": {"default_value": true}, "speed_infill": {"value": "speed_print"}, "speed_support": {"value": "speed_wall_0"}, "speed_wall_x": {"value": "speed_wall"}}}