2025-07-18T10:49:44.0079185Z ##[group]Run echo "=== Debugging cura_inst directory ==="
2025-07-18T10:49:44.0079550Z [36;1mecho "=== Debugging cura_inst directory ==="[0m
2025-07-18T10:49:44.0079658Z [36;1mif exist "cura_inst" ([0m
2025-07-18T10:49:44.0079753Z [36;1m    echo "cura_inst directory exists"[0m
2025-07-18T10:49:44.0079823Z [36;1m    dir cura_inst[0m
2025-07-18T10:49:44.0079968Z [36;1m    echo "Checking for conanrun.bat in build\generators:"[0m
2025-07-18T10:49:44.0080092Z [36;1m    if exist "cura_inst\build\generators\conanrun.bat" ([0m
2025-07-18T10:49:44.0080198Z [36;1m        echo "conanrun.bat found in build\generators"[0m
2025-07-18T10:49:44.0080304Z [36;1m        type cura_inst\build\generators\conanrun.bat[0m
2025-07-18T10:49:44.0080380Z [36;1m    ) else ([0m
2025-07-18T10:49:44.0080517Z [36;1m        echo "ERROR: conanrun.bat not found in build\generators!"[0m
2025-07-18T10:49:44.0080608Z [36;1m        echo "Available files in cura_inst:"[0m
2025-07-18T10:49:44.0080678Z [36;1m        dir cura_inst /s[0m
2025-07-18T10:49:44.0080776Z [36;1m        exit /b 1[0m
2025-07-18T10:49:44.0080829Z [36;1m    )[0m
2025-07-18T10:49:44.0080888Z [36;1m) else ([0m
2025-07-18T10:49:44.0080997Z [36;1m    echo "ERROR: cura_inst directory not found!"[0m
2025-07-18T10:49:44.0081053Z [36;1m    exit /b 1[0m
2025-07-18T10:49:44.0081103Z [36;1m)[0m
2025-07-18T10:49:44.0081158Z [36;1m[0m
2025-07-18T10:49:44.0081262Z [36;1mecho "=== Calling virtual environment setup ==="[0m
2025-07-18T10:49:44.0081346Z [36;1mecho "First calling conanrun.bat:"[0m
2025-07-18T10:49:44.0081446Z [36;1mcall cura_inst\build\generators\conanrun.bat[0m
2025-07-18T10:49:44.0081651Z [36;1mif %ERRORLEVEL% neq 0 ([0m
2025-07-18T10:49:44.0081786Z [36;1m    echo "conanrun.bat failed with error level %ERRORLEVEL%"[0m
2025-07-18T10:49:44.0081852Z [36;1m    exit /b %ERRORLEVEL%[0m
2025-07-18T10:49:44.0081905Z [36;1m)[0m
2025-07-18T10:49:44.0081954Z [36;1m[0m
2025-07-18T10:49:44.0082053Z [36;1mecho "Then calling virtual_python_env.bat:"[0m
2025-07-18T10:49:44.0082190Z [36;1mcall cura_inst\build\generators\virtual_python_env.bat[0m
2025-07-18T10:49:44.0082258Z [36;1mif %ERRORLEVEL% neq 0 ([0m
2025-07-18T10:49:44.0082415Z [36;1m    echo "virtual_python_env.bat failed with error level %ERRORLEVEL%"[0m
2025-07-18T10:49:44.0082479Z [36;1m    exit /b %ERRORLEVEL%[0m
2025-07-18T10:49:44.0082533Z [36;1m)[0m
2025-07-18T10:49:44.0082584Z [36;1m[0m
2025-07-18T10:49:44.0082700Z [36;1mecho "=== Creating Python virtual environment ==="[0m
2025-07-18T10:49:44.0082793Z [36;1mpython -m venv cura_installer_venv[0m
2025-07-18T10:49:44.0082897Z [36;1mcall cura_installer_venv\Scripts\Activate.bat[0m
2025-07-18T10:49:44.0082949Z [36;1m[0m
2025-07-18T10:49:44.0083044Z [36;1mecho "=== Checking Python environment ==="[0m
2025-07-18T10:49:44.0083109Z [36;1mpython --version[0m
2025-07-18T10:49:44.0083170Z [36;1mpip --version[0m
2025-07-18T10:49:44.0083244Z [36;1mecho "Python path: %PYTHON%"[0m
2025-07-18T10:49:44.0083324Z [36;1mecho "Current PATH: %PATH%"[0m
2025-07-18T10:49:44.0083372Z [36;1m[0m
2025-07-18T10:49:44.0083560Z [36;1mecho "=== Checking for pip requirements files ==="[0m
2025-07-18T10:49:44.0083656Z [36;1mif exist "cura_inst\build\generators" ([0m
2025-07-18T10:49:44.0083738Z [36;1m    echo "generators directory exists"[0m
2025-07-18T10:49:44.0083812Z [36;1m    dir cura_inst\build\generators[0m
2025-07-18T10:49:44.0083869Z [36;1m) else ([0m
2025-07-18T10:49:44.0083969Z [36;1m    echo "ERROR: generators directory not found!"[0m
2025-07-18T10:49:44.0084043Z [36;1m    echo "Contents of cura_inst:"[0m
2025-07-18T10:49:44.0084104Z [36;1m    dir cura_inst /s[0m
2025-07-18T10:49:44.0084167Z [36;1m    exit /b 1[0m
2025-07-18T10:49:44.0084216Z [36;1m)[0m
2025-07-18T10:49:44.0084264Z [36;1m[0m
2025-07-18T10:49:44.0084441Z [36;1mif exist "cura_inst\build\generators\pip_requirements_core_basic.txt" ([0m
2025-07-18T10:49:44.0084541Z [36;1m    echo "Installing core basic requirements..."[0m
2025-07-18T10:49:44.0086435Z [36;1m    python -m pip install -r cura_inst\build\generators\pip_requirements_core_basic.txt --no-warn-script-location[0m
2025-07-18T10:49:44.0086509Z [36;1m) else ([0m
2025-07-18T10:49:44.0086649Z [36;1m    echo "WARNING: pip_requirements_core_basic.txt not found"[0m
2025-07-18T10:49:44.0086700Z [36;1m)[0m
2025-07-18T10:49:44.0086750Z [36;1m[0m
2025-07-18T10:49:44.0086940Z [36;1mif exist "cura_inst\build\generators\pip_requirements_core_hashes.txt" ([0m
2025-07-18T10:49:44.0087054Z [36;1m    echo "Installing core hashes requirements..."[0m
2025-07-18T10:49:44.0087355Z [36;1m    python -m pip install -r cura_inst\build\generators\pip_requirements_core_hashes.txt --no-warn-script-location[0m
2025-07-18T10:49:44.0087417Z [36;1m) else ([0m
2025-07-18T10:49:44.0087558Z [36;1m    echo "WARNING: pip_requirements_core_hashes.txt not found"[0m
2025-07-18T10:49:44.0087607Z [36;1m)[0m
2025-07-18T10:49:44.0087661Z [36;1m[0m
2025-07-18T10:49:44.0087850Z [36;1mif exist "cura_inst\build\generators\pip_requirements_installer_basic.txt" ([0m
2025-07-18T10:49:44.0087954Z [36;1m    echo "Installing installer requirements..."[0m
2025-07-18T10:49:44.0088259Z [36;1m    python -m pip install -r cura_inst\build\generators\pip_requirements_installer_basic.txt --no-warn-script-location[0m
2025-07-18T10:49:44.0088313Z [36;1m) else ([0m
2025-07-18T10:49:44.0088454Z [36;1m    echo "WARNING: pip_requirements_installer_basic.txt not found"[0m
2025-07-18T10:49:44.0088508Z [36;1m)[0m
2025-07-18T10:49:44.0088564Z [36;1m[0m
2025-07-18T10:49:44.0088663Z [36;1mecho "=== Running prepare_installer.py ==="[0m
2025-07-18T10:49:44.0089223Z [36;1mpython Cura-workflows\runner_scripts\prepare_installer.py --os Windows --architecture X64   --summary-output %GITHUB_STEP_SUMMARY% --variables-output %GITHUB_OUTPUT%[0m
2025-07-18T10:49:44.0089295Z [36;1mif %ERRORLEVEL% neq 0 ([0m
2025-07-18T10:49:44.0089456Z [36;1m    echo "prepare_installer.py failed with error level %ERRORLEVEL%"[0m
2025-07-18T10:49:44.0089530Z [36;1m    exit /b %ERRORLEVEL%[0m
2025-07-18T10:49:44.0089588Z [36;1m)[0m
2025-07-18T10:49:44.0089640Z [36;1m[0m
2025-07-18T10:49:44.0089756Z [36;1mecho "=== Checking for pyinstaller spec file ==="[0m
2025-07-18T10:49:44.0089865Z [36;1mif exist "cura_inst\UltiMaker-Cura.spec" ([0m
2025-07-18T10:49:44.0090014Z [36;1m    echo "UltiMaker-Cura.spec found, running pyinstaller..."[0m
2025-07-18T10:49:44.0090117Z [36;1m    pyinstaller cura_inst\UltiMaker-Cura.spec[0m
2025-07-18T10:49:44.0090178Z [36;1m) else ([0m
2025-07-18T10:49:44.0090278Z [36;1m    echo "ERROR: UltiMaker-Cura.spec not found"[0m
2025-07-18T10:49:44.0090358Z [36;1m    echo "Contents of cura_inst:"[0m
2025-07-18T10:49:44.0090428Z [36;1m    dir cura_inst[0m
2025-07-18T10:49:44.0090484Z [36;1m    exit /b 1[0m
2025-07-18T10:49:44.0090534Z [36;1m)[0m
2025-07-18T10:49:44.0109347Z shell: C:\Windows\system32\cmd.EXE /D /E:ON /V:OFF /S /C "CALL "{0}""
2025-07-18T10:49:44.0109421Z env:
2025-07-18T10:49:44.0109502Z   WIN_CERT_INSTALLER_CER: 
2025-07-18T10:49:44.0109664Z   WIN_CERT_INSTALLER_CER_PASS: 
2025-07-18T10:49:44.0109727Z   SENTRY_TOKEN: 
2025-07-18T10:49:44.0109785Z ##[endgroup]
2025-07-18T10:49:44.0216712Z "=== Debugging cura_inst directory ==="
2025-07-18T10:49:44.0220862Z "cura_inst directory exists"
2025-07-18T10:49:44.0227471Z  Volume in drive D is Temporary Storage
2025-07-18T10:49:44.0227831Z  Volume Serial Number is B4DF-FF38
2025-07-18T10:49:44.0227884Z 
2025-07-18T10:49:44.0228046Z  Directory of D:\a\Cura\Cura\cura_inst
2025-07-18T10:49:44.0228059Z 
2025-07-18T10:49:44.0229196Z 07/18/2025  10:48 AM    <DIR>          .
2025-07-18T10:49:44.0229359Z 07/18/2025  10:48 AM    <DIR>          ..
2025-07-18T10:49:44.0229654Z 07/18/2025  10:48 AM    <DIR>          build
2025-07-18T10:49:44.0229847Z                0 File(s)              0 bytes
2025-07-18T10:49:44.0230700Z                3 Dir(s)  156,157,550,592 bytes free
2025-07-18T10:49:44.0232463Z "Checking for conanrun.bat in build\generators:"
2025-07-18T10:49:44.0233963Z "conanrun.bat found in build\generators"
2025-07-18T10:49:44.0235721Z @echo off
2025-07-18T10:49:44.0239174Z call "%~dp0/virtual_python_env.bat""=== Calling virtual environment setup ==="
2025-07-18T10:49:44.0241914Z "First calling conanrun.bat:"
2025-07-18T10:49:44.1167975Z "Then calling virtual_python_env.bat:"
2025-07-18T10:49:44.2075217Z "=== Creating Python virtual environment ==="
2025-07-18T10:49:48.8081567Z "=== Checking Python environment ==="
2025-07-18T10:49:48.8187033Z Python 3.12.2
2025-07-18T10:49:49.1739551Z pip 25.1.1 from D:\a\Cura\Cura\cura_inst\build\generators\cura_venv\Lib\site-packages\pip (python 3.12)
2025-07-18T10:49:49.1740210Z 
2025-07-18T10:49:49.2074945Z "Python path: ;C:\Users\<USER>\.conan2\p\cpyth130b4b9567088\p\bin\python.exe;C:\Users\<USER>\.conan2\p\cpyth130b4b9567088\p\bin\python.exe"
2025-07-18T10:49:49.2090761Z "Current PATH: D:\a\Cura\Cura\cura_installer_venv\Scripts;D:\a\Cura\Cura\cura_inst\build\generators\\..\..\build\generators\cura_venv\Scripts;C:\Users\<USER>\.conan2\p\b\arcusaa0a979f619a7\p\bin;C:\Users\<USER>\.conan2\p\savit738719a50af02\p\bin;C:\Users\<USER>\.conan2\p\b\nest2de2c0eb88d898\p\bin;C:\Users\<USER>\.conan2\p\nloptb3c43de892ea1\p\bin;C:\Users\<USER>\.conan2\p\b\clippdfcb8e6a28aa3\p\bin;C:\Users\<USER>\.conan2\p\cpyth130b4b9567088\p\bin;D:\a\Cura\Cura\cura_inst\build\generators\\..\..\build\generators\cura_venv\Scripts;C:\Users\<USER>\.conan2\p\b\arcusaa0a979f619a7\p\bin;C:\Users\<USER>\.conan2\p\savit738719a50af02\p\bin;C:\Users\<USER>\.conan2\p\b\nest2de2c0eb88d898\p\bin;C:\Users\<USER>\.conan2\p\nloptb3c43de892ea1\p\bin;C:\Users\<USER>\.conan2\p\b\clippdfcb8e6a28aa3\p\bin;C:\Users\<USER>\.conan2\p\cpyth130b4b9567088\p\bin;C:\Program Files\MongoDB\Server\5.0\bin;C:\aliyun-cli;C:\vcpkg;C:\Program Files (x86)\NSIS\;C:\tools\zstd;C:\Program Files\Mercurial\;C:\hostedtoolcache\windows\stack\3.7.1\x64;C:\cabal\bin;C:\\ghcup\bin;C:\mingw64\bin;C:\Program Files\dotnet;C:\Program Files\MySQL\MySQL Server 8.0\bin;C:\Program Files\R\R-4.4.2\bin\x64;C:\SeleniumWebDrivers\GeckoDriver;C:\SeleniumWebDrivers\EdgeDriver\;C:\SeleniumWebDrivers\ChromeDriver;C:\Program Files (x86)\sbt\bin;C:\Program Files (x86)\GitHub CLI;C:\Program Files\Git\bin;C:\Program Files (x86)\pipx_bin;C:\npm\prefix;C:\hostedtoolcache\windows\go\1.24.4\x64\bin;C:\hostedtoolcache\windows\Python\3.9.13\x64\Scripts;C:\hostedtoolcache\windows\Python\3.9.13\x64;C:\hostedtoolcache\windows\Ruby\3.3.8\x64\bin;C:\Program Files\OpenSSL\bin;C:\tools\kotlinc\bin;C:\hostedtoolcache\windows\Java_Temurin-Hotspot_jdk\8.0.452-9\x64\bin;C:\Program Files\ImageMagick-7.1.1-Q16-HDRI;C:\Program Files\Microsoft SDKs\Azure\CLI2\wbin;C:\ProgramData\kind;C:\ProgramData\Chocolatey\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\PowerShell\7\;C:\Program Files\Microsoft\Web Platform Installer\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files (x86)\WiX Toolset v3.14\bin;C:\Program Files\Microsoft SQL Server\130\DTS\Binn\;C:\Program Files\Microsoft SQL Server\140\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\ProgramData\chocolatey\lib\pulumi\tools\Pulumi\bin;C:\Program Files\CMake\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.9.10\bin;C:\Program Files\Microsoft Service Fabric\bin\Fabric\Fabric.Code;C:\Program Files\Microsoft SDKs\Service Fabric\Tools\ServiceFabricLocalClusterManager;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Program Files\GitHub CLI\;c:\tools\php;C:\Program Files (x86)\sbt\bin;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\Amazon\SessionManagerPlugin\bin\;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\LLVM\bin;C:\Program Files (x86)\LLVM\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.conan2\p\cpyth130b4b9567088\p\bin;windows\arduino\amd64;windows\arduino\CP210x_6.7.4;windows\arduino\FTDI USB Drivers\amd64;C:\Users\<USER>\.conan2\p\cpyth130b4b9567088\p\bin;windows\arduino\amd64;windows\arduino\CP210x_6.7.4;windows\arduino\FTDI USB Drivers\amd64"
2025-07-18T10:49:49.2105915Z "=== Checking for pip requirements files ==="
2025-07-18T10:49:49.2106358Z "generators directory exists"
2025-07-18T10:49:49.2106713Z  Volume in drive D is Temporary Storage
2025-07-18T10:49:49.2107110Z  Volume Serial Number is B4DF-FF38
2025-07-18T10:49:49.2107381Z 
2025-07-18T10:49:49.2107574Z  Directory of D:\a\Cura\Cura\cura_inst\build\generators
2025-07-18T10:49:49.2107921Z 
2025-07-18T10:49:49.2108046Z 07/18/2025  10:49 AM    <DIR>          .
2025-07-18T10:49:49.2108421Z 07/18/2025  10:48 AM    <DIR>          ..
2025-07-18T10:49:49.2108836Z 07/18/2025  10:49 AM                56 conanbuild.bat
2025-07-18T10:49:49.2109338Z 07/18/2025  10:49 AM             1,127 conanbuildenv-release-x86_64.bat
2025-07-18T10:49:49.2109653Z 07/18/2025  10:49 AM                46 conanrun.bat
2025-07-18T10:49:49.2109918Z 07/18/2025  10:49 AM    <DIR>          cura_venv
2025-07-18T10:49:49.2110192Z 07/18/2025  10:49 AM                67 deactivate_conanbuild.bat
2025-07-18T10:49:49.2111238Z 07/18/2025  10:49 AM                57 deactivate_conanrun.bat
2025-07-18T10:49:49.2111552Z 07/18/2025  10:49 AM             4,401 deactivate_virtual_python_env.bat
2025-07-18T10:49:49.2111878Z 07/18/2025  10:48 AM                74 pip_requirements_core_basic.txt
2025-07-18T10:49:49.2112206Z 07/18/2025  10:48 AM            36,521 pip_requirements_core_hashes.txt
2025-07-18T10:49:49.2112519Z 07/18/2025  10:48 AM                49 pip_requirements_dev_basic.txt
2025-07-18T10:49:49.2112838Z 07/18/2025  10:48 AM                45 pip_requirements_installer_basic.txt
2025-07-18T10:49:49.2113152Z 07/18/2025  10:48 AM             1,097 pip_requirements_summary.yml
2025-07-18T10:49:49.2113452Z 07/18/2025  10:48 AM             2,303 virtual_python_env.bat
2025-07-18T10:49:49.2113711Z               12 File(s)         45,843 bytes
2025-07-18T10:49:49.2113937Z                3 Dir(s)  156,139,319,296 bytes free
2025-07-18T10:49:49.2114172Z "Installing core basic requirements..."
2025-07-18T10:49:49.8516324Z Collecting charon@ git+https://github.com/ultimaker/libcharon@master/s-line#egg=charon (from -r cura_inst\build\generators\pip_requirements_core_basic.txt (line 1))
2025-07-18T10:49:49.8521985Z   Cloning https://github.com/ultimaker/libcharon (to revision master/s-line) to c:\users\<USER>\appdata\local\temp\pip-install-cx0e4tjz\charon_3d34095a2854419bbe4fd404a2a2dfec
2025-07-18T10:49:49.8739613Z   Running command git clone --filter=blob:none --quiet https://github.com/ultimaker/libcharon 'C:\Users\<USER>\AppData\Local\Temp\pip-install-cx0e4tjz\charon_3d34095a2854419bbe4fd404a2a2dfec'
2025-07-18T10:49:50.7528579Z   Running command git checkout -q b275d32cd2be7261194b6770cbd524717332e674
2025-07-18T10:49:51.0511257Z   Resolved https://github.com/ultimaker/libcharon to commit b275d32cd2be7261194b6770cbd524717332e674
2025-07-18T10:49:51.0754236Z   Preparing metadata (setup.py): started
2025-07-18T10:49:51.4600425Z   Preparing metadata (setup.py): finished with status 'done'
2025-07-18T10:49:51.7620401Z "Installing core hashes requirements..."
2025-07-18T10:49:52.4752950Z DEPRECATION: Wheel filename 'numpy-mkl-1.26.1-cp312-cp312-win_amd64.whl' is not correctly normalised. Future versions of pip will raise the following error:
2025-07-18T10:49:52.4754385Z Invalid wheel filename (invalid version): 'numpy-mkl-1.26.1-cp312-cp312-win_amd64'
2025-07-18T10:49:52.4755301Z 
2025-07-18T10:49:52.4756643Z  pip 25.3 will enforce this behaviour change. A possible replacement is to rename the wheel to use a correctly normalised name (this may require updating the version in the project metadata). Discussion can be found at https://github.com/pypa/pip/issues/12938
2025-07-18T10:49:52.4764956Z Collecting numpy@ https://cura.jfrog.io/artifactory/cura-local-pypi/numpy/numpy/numpy-mkl-1.26.1-cp312-cp312-win_amd64.whl (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 38))
2025-07-18T10:49:52.8227527Z   Downloading https://cura.jfrog.io/artifactory/cura-local-pypi/numpy/numpy/numpy-mkl-1.26.1-cp312-cp312-win_amd64.whl (249.3 MB)
2025-07-18T10:50:00.8307065Z      ------------------------------------- 249.3/249.3 MB 31.2 MB/s eta 0:00:00
2025-07-18T10:50:01.3230859Z Requirement already satisfied: certifi==2023.5.7 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 1)) (2023.5.7)
2025-07-18T10:50:01.3235986Z Requirement already satisfied: zeroconf==0.31.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 2)) (0.31.0)
2025-07-18T10:50:01.3241350Z Requirement already satisfied: importlib-metadata==4.10.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 3)) (4.10.0)
2025-07-18T10:50:01.3246380Z Requirement already satisfied: trimesh==3.9.36 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 4)) (3.9.36)
2025-07-18T10:50:01.3251677Z Requirement already satisfied: setuptools==75.6.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 5)) (75.6.0)
2025-07-18T10:50:01.3256732Z Requirement already satisfied: sentry-sdk==0.13.5 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 6)) (0.13.5)
2025-07-18T10:50:01.3261841Z Requirement already satisfied: pyserial==3.4 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 7)) (3.4)
2025-07-18T10:50:01.3266915Z Requirement already satisfied: chardet==3.0.4 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 8)) (3.0.4)
2025-07-18T10:50:01.3271903Z Requirement already satisfied: idna==2.8 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 9)) (2.8)
2025-07-18T10:50:01.3276850Z Requirement already satisfied: attrs==21.3.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 10)) (21.3.0)
2025-07-18T10:50:01.3281828Z Requirement already satisfied: requests==2.32.3 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 11)) (2.32.3)
2025-07-18T10:50:01.3286806Z Requirement already satisfied: twisted==21.2.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 12)) (21.2.0)
2025-07-18T10:50:01.3291767Z Requirement already satisfied: constantly==15.1.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 13)) (15.1.0)
2025-07-18T10:50:01.3296644Z Requirement already satisfied: hyperlink==21.0.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 14)) (21.0.0)
2025-07-18T10:50:01.3302616Z Requirement already satisfied: incremental==22.10.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 15)) (22.10.0)
2025-07-18T10:50:01.3309181Z Requirement already satisfied: zope.interface==5.4.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 16)) (5.4.0)
2025-07-18T10:50:01.3314304Z Requirement already satisfied: automat==20.2.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 17)) (20.2.0)
2025-07-18T10:50:01.3320039Z Requirement already satisfied: shapely==2.0.6 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 18)) (2.0.6)
2025-07-18T10:50:01.3325205Z Requirement already satisfied: cython==0.29.26 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 19)) (0.29.26)
2025-07-18T10:50:01.3330134Z Requirement already satisfied: pybind11==2.6.2 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 20)) (2.6.2)
2025-07-18T10:50:01.3335030Z Requirement already satisfied: wheel==0.37.1 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 21)) (0.37.1)
2025-07-18T10:50:01.3340000Z Requirement already satisfied: ifaddr==0.1.7 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 22)) (0.1.7)
2025-07-18T10:50:01.3344825Z Requirement already satisfied: pycparser==2.22 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 23)) (2.22)
2025-07-18T10:50:01.3349684Z Requirement already satisfied: zipp==3.5.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 24)) (3.5.0)
2025-07-18T10:50:01.3354610Z Requirement already satisfied: urllib3==2.2.3 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 25)) (2.2.3)
2025-07-18T10:50:01.3359472Z Requirement already satisfied: jeepney==0.8.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 26)) (0.8.0)
2025-07-18T10:50:01.3364577Z Requirement already satisfied: SecretStorage==3.3.3 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 27)) (3.3.3)
2025-07-18T10:50:01.3369390Z Requirement already satisfied: keyring==25.5.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 28)) (25.5.0)
2025-07-18T10:50:01.3375404Z Requirement already satisfied: jaraco.classes==3.4.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 29)) (3.4.0)
2025-07-18T10:50:01.3380920Z Requirement already satisfied: jaraco.functools==4.1.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 30)) (4.1.0)
2025-07-18T10:50:01.3385846Z Requirement already satisfied: jaraco.context==6.0.1 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 31)) (6.0.1)
2025-07-18T10:50:01.3390924Z Requirement already satisfied: more_itertools==10.5.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 32)) (10.5.0)
2025-07-18T10:50:01.3396063Z Requirement already satisfied: charset-normalizer==2.1.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 33)) (2.1.0)
2025-07-18T10:50:01.3401138Z Requirement already satisfied: twisted-iocpsupport==1.0.2 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 34)) (1.0.2)
2025-07-18T10:50:01.3405987Z Requirement already satisfied: pywin32-ctypes==0.2.3 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 35)) (0.2.3)
2025-07-18T10:50:01.3410708Z Requirement already satisfied: pynavlib==0.9.4 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 36)) (0.9.4)
2025-07-18T10:50:01.3416004Z Requirement already satisfied: colorama==0.4.5 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 37)) (0.4.5)
2025-07-18T10:50:01.3427507Z Requirement already satisfied: PyQt6==6.6.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 39)) (6.6.0)
2025-07-18T10:50:01.3432720Z Requirement already satisfied: PyQt6-Qt6==6.6.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 40)) (6.6.0)
2025-07-18T10:50:01.3437586Z Requirement already satisfied: PyQt6-sip==13.6.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 41)) (13.6.0)
2025-07-18T10:50:01.3442484Z Requirement already satisfied: cffi==1.17.1 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 42)) (1.17.1)
2025-07-18T10:50:01.3447255Z Requirement already satisfied: colorlog==6.6.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 43)) (6.6.0)
2025-07-18T10:50:01.3452211Z Requirement already satisfied: cryptography==44.0.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 44)) (44.0.0)
2025-07-18T10:50:01.3457057Z Requirement already satisfied: mypy==0.931 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 45)) (0.931)
2025-07-18T10:50:01.3462188Z Requirement already satisfied: mypy-extensions==0.4.3 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 46)) (0.4.3)
2025-07-18T10:50:01.3467367Z Requirement already satisfied: networkx==2.6.2 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 47)) (2.6.2)
2025-07-18T10:50:01.3472555Z Requirement already satisfied: numpy-stl==2.10.1 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 48)) (2.10.1)
2025-07-18T10:50:01.3477613Z Requirement already satisfied: pyclipper==1.3.0.post5 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 49)) (1.3.0.post5)
2025-07-18T10:50:01.3482348Z Requirement already satisfied: python-utils==2.3.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 50)) (2.3.0)
2025-07-18T10:50:01.3487170Z Requirement already satisfied: scipy==1.11.3 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 51)) (1.11.3)
2025-07-18T10:50:01.3491954Z Requirement already satisfied: six==1.16.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 52)) (1.16.0)
2025-07-18T10:50:01.3496783Z Requirement already satisfied: tomli==2.0.1 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 53)) (2.0.1)
2025-07-18T10:50:01.3502321Z Requirement already satisfied: typing-extensions==4.3.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from -r cura_inst\build\generators\pip_requirements_core_hashes.txt (line 54)) (4.3.0)
2025-07-18T10:50:01.6951450Z "Installing installer requirements..."
2025-07-18T10:50:02.4577382Z Collecting pyinstaller==6.11.1 (from -r cura_inst\build\generators\pip_requirements_installer_basic.txt (line 1))
2025-07-18T10:50:02.4904590Z   Downloading pyinstaller-6.11.1-py3-none-win_amd64.whl.metadata (8.3 kB)
2025-07-18T10:50:02.5625736Z Collecting pyinstaller-hooks-contrib (from -r cura_inst\build\generators\pip_requirements_installer_basic.txt (line 2))
2025-07-18T10:50:02.5719785Z   Downloading pyinstaller_hooks_contrib-2025.6-py3-none-any.whl.metadata (16 kB)
2025-07-18T10:50:02.6021356Z Requirement already satisfied: setuptools>=42.0.0 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from pyinstaller==6.11.1->-r cura_inst\build\generators\pip_requirements_installer_basic.txt (line 1)) (75.6.0)
2025-07-18T10:50:02.6450580Z Collecting altgraph (from pyinstaller==6.11.1->-r cura_inst\build\generators\pip_requirements_installer_basic.txt (line 1))
2025-07-18T10:50:02.6501894Z   Downloading altgraph-0.17.4-py2.py3-none-any.whl.metadata (7.3 kB)
2025-07-18T10:50:02.7205173Z Collecting packaging>=22.0 (from pyinstaller==6.11.1->-r cura_inst\build\generators\pip_requirements_installer_basic.txt (line 1))
2025-07-18T10:50:02.7259673Z   Downloading packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
2025-07-18T10:50:02.7833139Z Collecting pefile!=2024.8.26,>=2022.5.30 (from pyinstaller==6.11.1->-r cura_inst\build\generators\pip_requirements_installer_basic.txt (line 1))
2025-07-18T10:50:02.7910117Z   Downloading pefile-2023.2.7-py3-none-any.whl.metadata (1.4 kB)
2025-07-18T10:50:02.8197905Z Requirement already satisfied: pywin32-ctypes>=0.2.1 in d:\a\cura\cura\cura_inst\build\generators\cura_venv\lib\site-packages (from pyinstaller==6.11.1->-r cura_inst\build\generators\pip_requirements_installer_basic.txt (line 1)) (0.2.3)
2025-07-18T10:50:02.8365423Z Downloading pyinstaller-6.11.1-py3-none-win_amd64.whl (1.3 MB)
2025-07-18T10:50:02.8876141Z    ---------------------------------------- 1.3/1.3 MB 33.7 MB/s eta 0:00:00
2025-07-18T10:50:02.8971060Z Downloading pyinstaller_hooks_contrib-2025.6-py3-none-any.whl (440 kB)
2025-07-18T10:50:02.9293341Z Downloading packaging-25.0-py3-none-any.whl (66 kB)
2025-07-18T10:50:02.9567355Z Downloading pefile-2023.2.7-py3-none-any.whl (71 kB)
2025-07-18T10:50:02.9894495Z Downloading altgraph-0.17.4-py2.py3-none-any.whl (21 kB)
2025-07-18T10:50:03.2037363Z Installing collected packages: altgraph, pefile, packaging, pyinstaller-hooks-contrib, pyinstaller
2025-07-18T10:50:04.7376698Z 
2025-07-18T10:50:04.7393989Z Successfully installed altgraph-0.17.4 packaging-25.0 pefile-2023.2.7 pyinstaller-6.11.1 pyinstaller-hooks-contrib-2025.6
2025-07-18T10:50:04.8094709Z "=== Running prepare_installer.py ==="
2025-07-18T10:50:04.8554418Z Traceback (most recent call last):
2025-07-18T10:50:04.8562085Z   File "D:\a\Cura\Cura\Cura-workflows\runner_scripts\prepare_installer.py", line 5, in <module>
2025-07-18T10:50:04.8562602Z     from cura import CuraVersion
2025-07-18T10:50:04.8562850Z ModuleNotFoundError: No module named 'cura'
2025-07-18T10:50:04.8622632Z "prepare_installer.py failed with error level 1"
2025-07-18T10:50:04.8647662Z ##[error]Process completed with exit code 1.
