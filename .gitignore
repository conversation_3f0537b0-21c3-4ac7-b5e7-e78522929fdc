# Compiled and generated things.
build
*.pyc
__pycache__
# *.mo  # Commented out to allow translation files to be committed
docs/html
*.log
resources/i18n/en_US
resources/i18n/en_7S
resources/i18n/x-test
resources/firmware
resources/materials
resources/images/whats_new
resources/texts/whats_new
#CuraEngine.exe
# LC_MESSAGES  # Commented out to allow translation files to be committed
.cache
*.qmlc
.mypy_cache
.pytest_cache

#MacOS
.DS_Store

# Editors and IDEs.
*kdev*
*.kate-swp
*.lprof
*~
*.qm
.directory
.idea
cura.desktop
*.bak

# Eclipse+PyDev
.project
.pydevproject
.settings

#Externally located plug-ins commonly installed by our devs.
plugins/BarbarianPlugin
plugins/cura-big-flame-graph
plugins/cura-camera-position
plugins/cura-god-mode-plugin
plugins/cura-siemensnx-plugin
plugins/CuraBlenderPlugin
plugins/CuraCloudPlugin
plugins/CuraDrivePlugin
plugins/CuraLiveScriptingPlugin
plugins/CuraOpenSCADPlugin
plugins/CuraPrintProfileCreator
plugins/CuraSolidWorksPlugin
plugins/CuraVariSlicePlugin
plugins/Doodle3D-cura-plugin
plugins/FlatProfileExporter
plugins/GodMode
plugins/OctoPrintPlugin
plugins/ProfileFlattener
plugins/SettingsGuide
plugins/SettingsGuide2
plugins/SVGToolpathReader
plugins/X3GWriter
plugins/CuraFlatPack
plugins/CuraRemoteSupport
plugins/ModelCutter
plugins/PrintProfileCreator
plugins/MultiPrintPlugin
plugins/CuraOrientationPlugin

#Build stuff
CMakeCache.txt
CMakeFiles
CPackSourceConfig.cmake
Testing/
CTestTestfile.cmake
Makefile*
junit-pytest-*
CuraVersion.py
cmake_install.cmake

#Debug
*.gcode
run.sh
.scannerwork/
CuraEngine

/.coverage

#Prevents import failures when plugin running tests
plugins/__init__.py

venv/
build/
dist/
conaninfo.txt
conan.lock
conan_imports_manifest.txt
conanbuildinfo.txt
graph_info.json
Ultimaker-Cura.spec
.run/
/printer-linter/src/printerlinter.egg-info/
/plugins/CuraEngineGradualFlow
/resources/bundled_packages/bundled_*.json
curaengine_plugin_gradual_flow
curaengine_plugin_gradual_flow.exe
