version: "5.11.0-alpha.0"
requirements:
  - "cura_resources/5.11.0-alpha.0@ultimaker/testing"
  #- "uranium/5.11.0-alpha.0@ultimaker/testing"
  - "uranium/5.11.0@wsd07/testing"
  #- "curaengine/5.11.0-alpha.0@ultimaker/testing"  # Using precompiled CuraEngine.exe instead
  - "curaengine/5.11.0@wsd07/testing"
  - "cura_binary_data/5.11.0-alpha.0@ultimaker/testing"
  - "fdm_materials/5.11.0-alpha.0@ultimaker/testing"
  - "dulcificum/5.10.0"
  - "pysavitar/5.11.0-alpha.0"
  - "pynest2d/5.10.0"
requirements_internal:
  - "fdm_materials/5.11.0-alpha.0@ultimaker/testing"
  - "cura_private_data/5.11.0-alpha.0@internal/testing"
requirements_enterprise:
  - "native_cad_plugin/2.0.0"
urls:
    default:
        cloud_api_root: "https://api.ultimaker.com"
        cloud_account_api_root: "https://account.ultimaker.com"
        marketplace_root: "https://marketplace.ultimaker.com"
        digital_factory_url: "https://digitalfactory.ultimaker.com"
        cura_latest_url: "https://software.ultimaker.com/latest.json"
    staging:
        cloud_api_root: "https://api-staging.ultimaker.com"
        cloud_account_api_root: "https://account-staging.ultimaker.com"
        marketplace_root: "https://marketplace-staging.ultimaker.com"
        digital_factory_url: "https://digitalfactory-staging.ultimaker.com"
        cura_latest_url: "https://software.ultimaker.com/latest.json"

pyinstaller:
    runinfo:
        entrypoint: "cura_app.py"
    datas:
        cura_plugins:
            package: "cura"
            src: "plugins"
            dst: "share/cura/plugins"
        native_cad_plugin:
            package: "native_cad_plugin"
            src: "res/plugins/NativeCADplugin"
            dst: "share/cura/plugins/NativeCADplugin"
            enterprise_only: true
        native_cad_plugin_bundled:
            package: "native_cad_plugin"
            src: "res/bundled_packages"
            dst: "share/cura/resources/bundled_packages"
            enterprise_only: true
        cura_resources:
            package: "cura"
            src: "resources"
            dst: "share/cura/resources"
        cura_shared_resources:
            package: "cura_resources"
            src: "res"
            dst: "share/cura/resources"
        cura_private_data:
            package: "cura_private_data"
            src: "res"
            dst: "share/cura"
            internal: true
        uranium_plugins:
            package: "uranium"
            src: "plugins"
            dst: "share/uranium/plugins"
        uranium_resources:
            package: "uranium"
            src: "resources"
            dst: "share/uranium/resources"
        uranium_um_qt_qml_um:
            package: "uranium"
            src: "site-packages/UM/Qt/qml/UM"
            dst: "PyQt6/Qt6/qml/UM"
        cura_binary_data:
            package: "cura_binary_data"
            src: "resources/cura/resources"
            dst: "share/cura/resources"
        uranium_binary_data:
            package: "cura_binary_data"
            src: "resources/uranium/resources"
            dst: "share/uranium/resources"
        windows_binary_data:
            package: "cura_binary_data"
            src: "windows"
            dst: "share/windows"
            oses:
              - "Windows"
        fdm_materials:
            package: "fdm_materials"
            src: "res/resources/materials"
            dst: "share/cura/resources/materials"
    binaries:
        curaengine:
            package: "curaengine"
            src: "bin"
            dst: "."
            binary: "CuraEngine"
    hiddenimports:
        - "pySavitar"
        - "pyArcus"
        - "pyDulcificum"
        - "pynest2d"
        - "PyQt6"
        - "PyQt6.QtNetwork"
        - "PyQt6.sip"
        - "logging.handlers"
        - "zeroconf"
        - "fcntl"
        - "stl"
        - "serial"
        - "win32cred"
        - "win32timezone"
        - "pkgutil"
    hiddenimports_WINDOWS_ONLY:
        - "PyQt6.Qt"
        - "PyQt6.Qt6"
    collect_all:
        - "cura"
        - "UM"
        - "serial"
        - "Charon"
        - "sqlite3"
        - "trimesh"
        - "win32ctypes"
        - "PyQt6.QtNetwork"
        - "PyQt6.sip"
        - "stl"
        - "keyrings.alt"
        - "pynavlib"
    collect_all_WINDOWS_ONLY:
        - "PyQt6.Qt"
        - "PyQt6.Qt6"
    icon:
        Windows: "./icons/Cura.ico"
        Macos: "./icons/cura.icns"
        Linux: "./icons/cura-128.png"
    blacklist:
      - [ "assimp" ]
      - [ "qt", "charts" ]
      - [ "qt", "coap" ]
      - [ "qt", "data", "vis" ]
      - [ "qt", "lab", "animat" ]
      - [ "qt", "mqtt" ]
      - [ "qt", "net", "auth" ]
      - [ "quick3d" ]
      - [ "qt", "timeline" ]
      - [ "qt", "virt", "key" ]
      - [ "qt", "wayland", "compos" ]
      - [ "qt", "5", "compat" ]

pycharm_targets:
  - jinja_path: .run_templates/pycharm_cura_run.run.xml.jinja
    module_name: Cura
    name: cura
    script_name: cura_app.py
  - jinja_path: .run_templates/pycharm_cura_run.run.xml.jinja
    module_name: Cura
    name: cura_external_engine
    parameters: --external-backend
    script_name: cura_app.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in tests
    script_name: tests/
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestBuildVolume.py
    script_name: tests/TestBuildVolume.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestConvexHullDecorator.py
    script_name: tests/TestConvexHullDecorator.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestCuraSceneNode.py
    script_name: tests/TestCuraSceneNode.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestCuraSceneNode.py
    script_name: tests/TestExtruderManager.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestGCodeListDecorator.py
    script_name: tests/TestGCodeListDecorator.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestHitChecker.py
    script_name: tests/TestHitChecker.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestIntentManager.py
    script_name: tests/TestIntentManager.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestLayer.py
    script_name: tests/TestLayer.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestMachineAction.py
    script_name: tests/TestMachineAction.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestMachineManager.py
    script_name: tests/TestMachineManager.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestOAuth2.py
    script_name: tests/TestOAuth2.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestObjectsModel.py
    script_name: tests/TestObjectsModel.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestPrintInformation.py
    script_name: tests/TestPrintInformation.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestPrintOrderManager.py
    script_name: tests/TestPrintOrderManager.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestProfileRequirements.py
    script_name: tests/TestProfileRequirements.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestThemes.py
    script_name: tests/TestThemes.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestContainerManager.py
    script_name: tests/Settings/TestContainerManager.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestCuraContainerRegistry.py
    script_name: tests/Settings/TestCuraContainerRegistry.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestCuraStackBuilder.py
    script_name: tests/Settings/TestCuraStackBuilder.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestDefinitionContainer.py
    script_name: tests/Settings/TestDefinitionContainer.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestExtruderStack.py
    script_name: tests/Settings/TestExtruderStack.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestGlobalStack.py
    script_name: tests/Settings/TestGlobalStack.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestProfiles.py
    script_name: tests/Settings/TestProfiles.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestSettingInheritanceManager.py
    script_name: tests/Settings/TestSettingInheritanceManager.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestSettingOverrideDecorator.py
    script_name: tests/Settings/TestSettingOverrideDecorator.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestSettingVisibilityPresets.py
    script_name: tests/Settings/TestSettingVisibilityPresets.py
  - jinja_path: .run_templates/pycharm_cura_test.run.xml.jinja
    module_name: Cura
    name: pytest in TestStartEndGCode.py
    script_name: tests/Machines/TestStartEndGCode.py

pip_requirements_core:
  any_os:
    charon:
      url: "git+https://github.com/ultimaker/libcharon@master/s-line#egg=charon"
    certifi:
      version: "2023.5.7"
      hashes:
        - sha256:c6c2e98f5c7869efca1f8916fed228dd91539f9f1b444c314c06eef02980c716
        - sha256:0f0d56dc5a6ad56fd4ba36484d6cc34451e1c6548c61daad8c320169f91eddc7
    zeroconf:
      version: "0.31.0"
      hashes:
        - sha256:5a468da018bc3f04bbce77ae247924d802df7aeb4c291bbbb5a9616d128800b0
        - sha256:53a180248471c6f81bd1fffcbce03ed93d7d8eaf10905c9121ac1ea996d19844
    importlib-metadata:
      version: "4.10.0"
      hashes:
        - sha256:b7cf7d3fef75f1e4c80a96ca660efbd51473d7e8f39b5ab9210febc7809012a4
        - sha256:92a8b58ce734b2a4494878e0ecf7d79ccd7a128b5fc6014c401e0b61f006f0f6
    trimesh:
      version: "3.9.36"
      hashes:
        - sha256:8ac8bea693b3ee119f11b022fc9b9481c9f1af06cb38bc859bf5d16bbbe49b23
        - sha256:f01e8edab14d1999700c980c21a1546f37417216ad915a53be649d263130181e
    setuptools:
      version: "75.6.0"
      hashes:
        - sha256:ce74b49e8f7110f9bf04883b730f4765b774ef3ef28f722cce7c273d253aaf7d
        - sha256:8199222558df7c86216af4f84c30e9b34a61d8ba19366cc914424cdbd28252f6
    sentry-sdk:
      version: "0.13.5"
      hashes:
        - sha256:05285942901d38c7ce2498aba50d8e87b361fc603281a5902dda98f3f8c5e145
        - sha256:c6b919623e488134a728f16326c6f0bcdab7e3f59e7f4c472a90eea4d6d8fe82
    pyserial:
      version: "3.4"
      hashes:
        - sha256:e0770fadba80c31013896c7e6ef703f72e7834965954a78e71a3049488d4d7d8
        - sha256:6e2d401fdee0eab996cf734e67773a0143b932772ca8b42451440cfed942c627
    chardet:
      version: "3.0.4"
      hashes:
        - sha256:fc323ffcaeaed0e0a02bf4d117757b98aed530d9ed4531e3e15460124c106691
        - sha256:84ab92ed1c4d4f16916e05906b6b75a6c0fb5db821cc65e70cbd64a3e2a5eaae
    idna:
      version: "2.8"
      hashes:
        - sha256:ea8b7f6188e6fa117537c3df7da9fc686d485087abf6ac197f9c46432f7e4a3c
        - sha256:c357b3f628cf53ae2c4c05627ecc484553142ca23264e593d327bcde5e9c3407
    attrs:
      version: "21.3.0"
      hashes:
        - sha256:8f7335278dedd26b58c38e006338242cc0977f06d51579b2b8b87b9b33bff66c
        - sha256:50f3c9b216dc9021042f71b392859a773b904ce1a029077f58f6598272432045
    requests:
      version: "2.32.3"
      hashes:
        - sha256:70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6
        - sha256:55365417734eb18255590a9ff9eb97e9e1da868d4ccd6402399eaf68af20a760
    twisted:
      version: "21.2.0"
      hashes:
        - sha256:aab38085ea6cda5b378b519a0ec99986874921ee8881318626b0a3414bb2631e
        - sha256:77544a8945cf69b98d2946689bbe0c75de7d145cdf11f391dd487eae8fc95a12
    constantly:
      version: "15.1.0"
      hashes:
        - sha256:dd2fa9d6b1a51a83f0d7dd76293d734046aa176e384bf6e33b7e44880eb37c5d
        - sha256:586372eb92059873e29eba4f9dec8381541b4d3834660707faf8ba59146dfc35
    hyperlink:
      version: "21.0.0"
      hashes:
        - sha256:e6b14c37ecb73e89c77d78cdb4c2cc8f3fb59a885c5b3f819ff4ed80f25af1b4
        - sha256:427af957daa58bc909471c6c40f74c5450fa123dd093fc53efd2e91d2705a56b
    incremental:
      version: "22.10.0"
      hashes:
        - sha256:b864a1f30885ee72c5ac2835a761b8fe8aa9c28b9395cacf27286602688d3e51
        - sha256:912feeb5e0f7e0188e6f42241d2f450002e11bbc0937c65865045854c24c0bd0
    zope.interface:
      version: "5.4.0"
      hashes:
        - sha256:7df1e1c05304f26faa49fa752a8c690126cf98b40b91d54e6e9cc3b7d6ffe8b7
        - sha256:2c98384b254b37ce50eddd55db8d381a5c53b4c10ee66e1e7fe749824f894021
        - sha256:08f9636e99a9d5410181ba0729e0408d3d8748026ea938f3b970a0249daa8192
        - sha256:0ea1d73b7c9dcbc5080bb8aaffb776f1c68e807767069b9ccdd06f27a161914a
        - sha256:273f158fabc5ea33cbc936da0ab3d4ba80ede5351babc4f577d768e057651531
        - sha256:f7ee479e96f7ee350db1cf24afa5685a5899e2b34992fb99e1f7c1b0b758d263
        - sha256:b0297b1e05fd128d26cc2460c810d42e205d16d76799526dfa8c8ccd50e74959
        - sha256:af310ec8335016b5e52cae60cda4a4f2a60a788cbb949a4fbea13d441aa5a09e
        - sha256:9a9845c4c6bb56e508651f005c4aeb0404e518c6f000d5a1123ab077ab769f5c
        - sha256:a1e6e96217a0f72e2b8629e271e1b280c6fa3fe6e59fa8f6701bec14e3354325
        - sha256:877473e675fdcc113c138813a5dd440da0769a2d81f4d86614e5d62b69497155
        - sha256:0b465ae0962d49c68aa9733ba92a001b2a0933c317780435f00be7ecb959c702
        - sha256:5dd9ca406499444f4c8299f803d4a14edf7890ecc595c8b1c7115c2342cadc5f
        - sha256:469e2407e0fe9880ac690a3666f03eb4c3c444411a5a5fddfdabc5d184a79f05
        - sha256:52de7fc6c21b419078008f697fd4103dbc763288b1406b4562554bd47514c004
        - sha256:3dd4952748521205697bc2802e4afac5ed4b02909bb799ba1fe239f77fd4e117
        - sha256:dd93ea5c0c7f3e25335ab7d22a507b1dc43976e1345508f845efc573d3d779d8
        - sha256:3748fac0d0f6a304e674955ab1365d515993b3a0a865e16a11ec9d86fb307f63
        - sha256:66c0061c91b3b9cf542131148ef7ecbecb2690d48d1612ec386de9d36766058f
        - sha256:d0c1bc2fa9a7285719e5678584f6b92572a5b639d0e471bb8d4b650a1a910920
        - sha256:2876246527c91e101184f63ccd1d716ec9c46519cc5f3d5375a3351c46467c46
        - sha256:334701327f37c47fa628fc8b8d28c7d7730ce7daaf4bda1efb741679c2b087fc
        - sha256:71aace0c42d53abe6fc7f726c5d3b60d90f3c5c055a447950ad6ea9cec2e37d9
        - sha256:5bb3489b4558e49ad2c5118137cfeaf59434f9737fa9c5deefc72d22c23822e2
        - sha256:1c0e316c9add0db48a5b703833881351444398b04111188069a26a61cfb4df78
        - sha256:6f0c02cbb9691b7c91d5009108f975f8ffeab5dff8f26d62e21c493060eff2a1
        - sha256:7d97a4306898b05404a0dcdc32d9709b7d8832c0c542b861d9a826301719794e
        - sha256:867a5ad16892bf20e6c4ea2aab1971f45645ff3102ad29bd84c86027fa99997b
        - sha256:5f931a1c21dfa7a9c573ec1f50a31135ccce84e32507c54e1ea404894c5eb96f
        - sha256:194d0bcb1374ac3e1e023961610dc8f2c78a0f5f634d0c737691e215569e640d
        - sha256:8270252effc60b9642b423189a2fe90eb6b59e87cbee54549db3f5562ff8d1b8
        - sha256:15e7d1f7a6ee16572e21e3576d2012b2778cbacf75eb4b7400be37455f5ca8bf
        - sha256:8892f89999ffd992208754851e5a052f6b5db70a1e3f7d54b17c5211e37a98c7
        - sha256:2e5a26f16503be6c826abca904e45f1a44ff275fdb7e9d1b75c10671c26f8b94
        - sha256:0f91b5b948686659a8e28b728ff5e74b1be6bf40cb04704453617e5f1e945ef3
        - sha256:4de4bc9b6d35c5af65b454d3e9bc98c50eb3960d5a3762c9438df57427134b8e
        - sha256:bf68f4b2b6683e52bec69273562df15af352e5ed25d1b6641e7efddc5951d1a7
        - sha256:63b82bb63de7c821428d513607e84c6d97d58afd1fe2eb645030bdc185440120
        - sha256:db1fa631737dab9fa0b37f3979d8d2631e348c3b4e8325d6873c2541d0ae5a48
        - sha256:f44e517131a98f7a76696a7b21b164bcb85291cee106a23beccce454e1f433a4
        - sha256:a9506a7e80bcf6eacfff7f804c0ad5350c8c95b9010e4356a4b36f5322f09abb
        - sha256:3c02411a3b62668200910090a0dff17c0b25aaa36145082a5a6adf08fa281e54
        - sha256:0cee5187b60ed26d56eb2960136288ce91bcf61e2a9405660d271d1f122a69a4
        - sha256:a8156e6a7f5e2a0ff0c5b21d6bcb45145efece1909efcbbbf48c56f8da68221d
        - sha256:205e40ccde0f37496904572035deea747390a8b7dc65146d30b96e2dd1359a83
        - sha256:3f24df7124c323fceb53ff6168da70dbfbae1442b4f3da439cd441681f54fe25
        - sha256:5208ebd5152e040640518a77827bdfcc73773a15a33d6644015b763b9c9febc1
        - sha256:17776ecd3a1fdd2b2cd5373e5ef8b307162f581c693575ec62e7c5399d80794c
        - sha256:d4d9d6c1a455d4babd320203b918ccc7fcbefe308615c521062bc2ba1aa4d26e
        - sha256:0cba8477e300d64a11a9789ed40ee8932b59f9ee05f85276dbb4b59acee5dd09
        - sha256:5dba5f530fec3f0988d83b78cc591b58c0b6eb8431a85edd1569a0539a8a5a0e
    automat:
      version: "20.2.0"
      hashes:
        - sha256:b6feb6455337df834f6c9962d6ccf771515b7d939bca142b29c20c2376bc6111
        - sha256:7979803c74610e11ef0c0d68a2942b152df52da55336e0c9d58daf1831cbdf33
    shapely:
      version: "2.0.6"
      hashes:
        - sha256:29a34e068da2d321e926b5073539fd2a1d4429a2c656bd63f0bd4c8f5b236d0b
        - sha256:e1c84c3f53144febf6af909d6b581bc05e8785d57e27f35ebaa5c1ab9baba13b
        - sha256:2ad2fae12dca8d2b727fa12b007e46fbc522148a584f5d6546c539f3464dccde
        - sha256:b3304883bd82d44be1b27a9d17f1167fda8c7f5a02a897958d86c59ec69b705e
        - sha256:3ec3a0eab496b5e04633a39fa3d5eb5454628228201fb24903d38174ee34565e
        - sha256:28f87cdf5308a514763a5c38de295544cb27429cfa655d50ed8431a4796090c4
        - sha256:5aeb0f51a9db176da9a30cb2f4329b6fbd1e26d359012bb0ac3d3c7781667a9e
        - sha256:9a7a78b0d51257a367ee115f4d41ca4d46edbd0dd280f697a8092dd3989867b2
        - sha256:f32c23d2f43d54029f986479f7c1f6e09c6b3a19353a3833c2ffb226fb63a855
        - sha256:b3dc9fb0eb56498912025f5eb352b5126f04801ed0e8bdbd867d21bdbfd7cbd0
        - sha256:d93b7e0e71c9f095e09454bf18dad5ea716fb6ced5df3cb044564a00723f339d
        - sha256:c02eb6bf4cfb9fe6568502e85bb2647921ee49171bcd2d4116c7b3109724ef9b
        - sha256:cec9193519940e9d1b86a3b4f5af9eb6910197d24af02f247afbfb47bcb3fab0
        - sha256:83b94a44ab04a90e88be69e7ddcc6f332da7c0a0ebb1156e1c4f568bbec983c3
        - sha256:537c4b2716d22c92036d00b34aac9d3775e3691f80c7aa517c2c290351f42cd8
        - sha256:98fea108334be345c283ce74bf064fa00cfdd718048a8af7343c59eb40f59726
        - sha256:42fd4cd4834747e4990227e4cbafb02242c0cffe9ce7ef9971f53ac52d80d55f
        - sha256:665990c84aece05efb68a21b3523a6b2057e84a1afbef426ad287f0796ef8a48
        - sha256:42805ef90783ce689a4dde2b6b2f261e2c52609226a0438d882e3ced40bb3013
        - sha256:6d2cb146191a47bd0cee8ff5f90b47547b82b6345c0d02dd8b25b88b68af62d7
        - sha256:e3fdef0a1794a8fe70dc1f514440aa34426cc0ae98d9a1027fb299d45741c381
        - sha256:2c665a0301c645615a107ff7f52adafa2153beab51daf34587170d85e8ba6805
        - sha256:0334bd51828f68cd54b87d80b3e7cee93f249d82ae55a0faf3ea21c9be7b323a
        - sha256:d37d070da9e0e0f0a530a621e17c0b8c3c9d04105655132a87cfff8bd77cc4c2
        - sha256:fa7468e4f5b92049c0f36d63c3e309f85f2775752e076378e36c6387245c5462
        - sha256:ed5867e598a9e8ac3291da6cc9baa62ca25706eea186117034e8ec0ea4355653
        - sha256:81d9dfe155f371f78c8d895a7b7f323bb241fb148d848a2bf2244f79213123fe
        - sha256:fbb7bf02a7542dba55129062570211cfb0defa05386409b3e306c39612e7fbcc
        - sha256:837d395fac58aa01aa544495b97940995211e3e25f9aaf87bc3ba5b3a8cd1ac7
        - sha256:c6d88ade96bf02f6bfd667ddd3626913098e243e419a0325ebef2bbd481d1eb6
        - sha256:8b3b818c4407eaa0b4cb376fd2305e20ff6df757bf1356651589eadc14aab41b
        - sha256:1bbc783529a21f2bd50c79cef90761f72d41c45622b3e57acf78d984c50a5d13
        - sha256:2423f6c0903ebe5df6d32e0066b3d94029aab18425ad4b07bf98c3972a6e25a1
        - sha256:2de00c3bfa80d6750832bde1d9487e302a6dd21d90cb2f210515cefdb616e5f5
        - sha256:3a82d58a1134d5e975f19268710e53bddd9c473743356c90d97ce04b73e101ee
        - sha256:392f66f458a0a2c706254f473290418236e52aa4c9b476a072539d63a2460595
        - sha256:eba5bae271d523c938274c61658ebc34de6c4b33fdf43ef7e938b5776388c1be
        - sha256:7060566bc4888b0c8ed14b5d57df8a0ead5c28f9b69fb6bed4476df31c51b0af
        - sha256:b02154b3e9d076a29a8513dffcb80f047a5ea63c897c0cd3d3679f29363cf7e5
        - sha256:44246d30124a4f1a638a7d5419149959532b99dfa25b54393512e6acc9c211ac
        - sha256:2b542d7f1dbb89192d3512c52b679c822ba916f93479fa5d4fc2fe4fa0b3c9e8
        - sha256:997f6159b1484059ec239cacaa53467fd8b5564dabe186cd84ac2944663b0bf6
    cython:
      version: "0.29.26"
      hashes:
        - sha256:c4b003b6b7aa9e74552ef8d4e6009b3e3c3e8fa585710b3a6d062e088e460c1b
        - sha256:ce804a021c92fea66c8c100781a111706f21bade7a546895c5a9c57fe2df8b24
        - sha256:93840f2071c1f15e613509eadee1fbcd335e8666772437fe5038e24059edd48c
        - sha256:10402f0f1564ffc6ecb9c45e07f995771d05bb0b0e1d151e40574638292ee3a5
        - sha256:8e07121b34221458a2151d37e137b8f5b011a9c51dd38db2499a6198590aa319
        - sha256:233a87db76941626f1db08f4b25a4a5b425b13b64ed0e673c3641f7b650a48d8
        - sha256:6773cce9d4b3b6168d8feb2b6f06b658ef1e11cbfec075041745666d8e2a5e45
        - sha256:c813799d533194b7d85203d881d8b4f567a8c644a67f50d47f1ffbf316df412f
        - sha256:362fbb9cb4627c7786231429768b54aaba5459a2a0e46c25e59f202ca6155437
        - sha256:2b834ff6e4d10ba6d7a0d676dd71c1b427a181ddbbbbf79e91d1861557aab59f
        - sha256:0c3093bc99facfc97e5019f6c5bc39987663792265c1334d9fc9e37c3a3dcd6f
        - sha256:bbf0149680c1fca07200a3ed372b22e6bad7851d191b717a61f9a68af370e180
        - sha256:a1cc55db32cd39474081d478263b96e036552cdbbab8831c90ea43fb385a9b66
        - sha256:ebe32e002a9e6553de399033e259ece72aa17c77f740b265e66f122572a8a278
        - sha256:6b385f68789c3e8a75b4827e8a4970ff04605ad3cb1c0b41005cc69368dad65d
        - sha256:1519eb639de308f5763eb0666b4cc7bd3958268f3f6228cc610b7b4d6c94b68b
        - sha256:e118525defec3f67471d8ee5ce04340d43195410a87e5d7ec8a1a9e953c0066a
        - sha256:706ea55f58c2722206e51cd9a8754ed0995c4c4231d24b095875d2641d745222
        - sha256:77913fe27c5e22c995bac090d01e200ff91e5f58aa944e2d2e94cbf67ea2ae34
        - sha256:51923120f57a42c59f5ee6bba9e89a31a394ae8cd419c753f64d8a3aea1ee8b7
        - sha256:82881565d04027728d7762edd8c085927a840873af7ee049d703e0ca226bc08d
        - sha256:531303085503959338e6cdac630626280ef111aecbb22d48321673a8c3897c0a
        - sha256:0205b685802eb4c039b14f67b7ac3f00c55ff04b9e3871df2249576d3e59ba42
        - sha256:7df94e56872df8f396ca669466fee60256f69f678654239f706b1e643c2ac4a5
        - sha256:4b7d04b393d9a4b5fec0cbc4b0f29fe083a9d862d95231a6e7608978bd661d7e
        - sha256:af91dd63ac5f1f7fc70dc91ea063f727db42b5eb934d1f3832611be18e25171e
        - sha256:d83dad8dc6c63706cb3178dc79010b3865b1345090727189d2cd61758a825ee8
        - sha256:ca10e9fde0eaba1407ab353ff07a26daaa3e4dbe356108a149e482d441f070dd
        - sha256:fec66cd0a48697fab903854566235aecf1084f62e3163d6589ae7335a1b4d448
        - sha256:b3041e45aefaa4449fd671902132c0ac1f72eedaedac745c0e1a70a13bf990bb
        - sha256:ed76fb98979f02b5e89079906071983a36f3634d3028b86f935cf0196f24fcaa
        - sha256:4d868e1a41f5123f51a20c1b8e82f7cb6fa3370c104e98e707f7c910e8cadad1
        - sha256:868f309095e557f06dc58723ae865e8c65cfedb2646a562bd8080c92d69e4e4b
        - sha256:be550b566345bf53b95616334793ce42a128cf1d9dcde1e28cbf7ce52ea61d6d
        - sha256:be13be1e2b9b7395588f2a23bfa692f2f3e6f7936ccf7825c83431b8c8c3452b
        - sha256:41ee918480371ae5e5851ba9b1ead5a183e24aedb27bf807c7405d124e943f40
        - sha256:c91b1ba0d43f7f7ccde8121c672207c7891735ddcc83496af1e0ab617ddc4aba
        - sha256:5ecf5cf5b57086cc6c1cfc76d6353bbd7023e95da32e0883f1302ca50e481c33
        - sha256:0ffce25bf50fa926ec6bf8d6f29650e7cb33fae445938c9880e1ce9b776353ef
        - sha256:5041adfef502d67ecd5e291a7cf645a37fed7a9dac557f40d491053f35204d00
        - sha256:5fd5db458c9d3d2c2abd047f3190624d9cce8a80a8e0ca0baa69cfd133a523bc
        - sha256:75eaa22911d2ec37a3841f77b710b178c805cd378b5e1c4fb82dbc35620d2062
        - sha256:3aed8c642e8fb27024bca46830b7f62335a44a92354acf708d6b8d050f945a3a
        - sha256:b5ca05c2bfba0c2480b5fd390ecffe46b8da574d887d600388d6e3caf3f99a88
        - sha256:f5e15ff892c8afad64931ee3dd723c4755c2c516606f9aae7613bebfac62b0f6
        - sha256:af377d543a762867da11fcf6e558f7a4a535ff8693f30cce123fab10c00fa312
    pybind11:
      version: "2.6.2"
      hashes:
        - sha256:2d8aebe1709bc367e34e3b23d8eccbf3f387ee9d5640548c6260d33b59f02405
        - sha256:d0e0aed9279656f21501243b707eb6e3b951e89e10c3271dedf3ae41c365e5ed
    wheel:
      version: "0.37.1"
      hashes:
        - sha256:4bdcd7d840138086126cd09254dc6195fb4fc6f01c050a1d7236f2630db1d22a
        - sha256:e9a504e793efbca1b8e0e9cb979a249cf4a0a7b5b8c9e8b65a5e39d49529c1c4
    ifaddr:
      version: "0.1.7"
      hashes:
        - sha256:d1f603952f0a71c9ab4e705754511e4e03b02565bc4cec7188ad6415ff534cd3
        - sha256:1f9e8a6ca6f16db5a37d3356f07b6e52344f6f9f7e806d618537731669eb1a94
    pycparser:
      version: "2.20"
      hashes:
        - sha256:7582ad22678f0fcd81102833f60ef8d0e57288b6b5fb00323d101be910e35705
        - sha256:2d475327684562c3a96cc71adf7dc8c4f0565175cf86b6d7a404ff4c771f15f0
    zipp:
      version: "3.5.0"
      hashes:
        - sha256:957cfda87797e389580cb8b9e3870841ca991e2125350677b2ca83a0e99390a3
        - sha256:f5812b1e007e48cff63449a5e9f4e7ebea716b4111f9c4f9a645f91d579bf0c4
    urllib3:
      version: "2.2.3"
      hashes:
        - sha256:ca899ca043dcb1bafa3e262d73aa25c465bfb49e0bd9dd5d59f1d0acba2f8fac
        - sha256:e7d814a81dad81e6caf2ec9fdedb284ecc9c73076b62654547cc64ccdcae26e9
    jeepney:
      version: "0.8.0"
      hashes:
        - sha256:c0a454ad016ca575060802ee4d590dd912e35c122fa04e70306de3d076cce755
        - sha256:5efe48d255973902f6badc3ce55e2aa6c5c3b3bc642059ef3a91247bcfcc5806
    SecretStorage:
      version: "3.3.3"
      hashes:
        - sha256:f356e6628222568e3af06f2eba8df495efa13b3b63081dafd4f7d9a7b7bc9f99
        - sha256:2403533ef369eca6d2ba81718576c5e0f564d5cca1b58f73a8b23e7d4eeebd77
    keyring:
      version: "25.5.0"
      hashes:
        - sha256:e67f8ac32b04be4714b42fe84ce7dad9c40985b9ca827c592cc303e7c26d9741
        - sha256:4c753b3ec91717fe713c4edd522d625889d8973a349b0e582622f49766de58e6
    jaraco.classes:
      version: "3.4.0"
      hashes:
        - sha256:f662826b6bed8cace05e7ff873ce0f9283b5c924470fe664fff1c2f00f581790
        - sha256:47a024b51d0239c0dd8c8540c6c7f484be3b8fcf0b2d85c13825780d3b3f3acd
    jaraco.functools:
      version: "4.1.0"
      hashes:
        - sha256:ad159f13428bc4acbf5541ad6dec511f91573b90fba04df61dafa2a1231cf649
        - sha256:70f7e0e2ae076498e212562325e805204fc092d7b4c17e0e86c959e249701a9d
    jaraco.context:
      version: "6.0.1"
      hashes:
        - sha256:f797fc481b490edb305122c9181830a3a5b76d84ef6d1aef2fb9b47ab956f9e4
        - sha256:9bae4ea555cf0b14938dc0aee7c9f32ed303aa20a3b73e7dc80111628792d1b3
    more_itertools:
      version: "10.5.0"
      hashes:
        - sha256:037b0d3203ce90cca8ab1defbbdac29d5f993fc20131f3664dc8d6acfa872aef
        - sha256:5482bfef7849c25dc3c6dd53a6173ae4795da2a41a80faea6700d9f5846c5da6
    charset-normalizer:
      version: "2.1.0"
      hashes:
        - sha256:5189b6f22b01957427f35b6a08d9a0bc45b46d3788ef5a92e978433c7a35f8a5
        - sha256:575e708016ff3a5e3681541cb9d79312c416835686d054a23accb873b254f413

  Windows:
    twisted-iocpsupport:
      version: "1.0.2"
      hashes:
        - sha256:985c06a33f5c0dae92c71a036d1ea63872ee86a21dd9b01e1f287486f15524b4
        - sha256:81b3abe3527b367da0220482820cb12a16c661672b7bcfcde328902890d63323
        - sha256:9dbb8823b49f06d4de52721b47de4d3b3026064ef4788ce62b1a21c57c3fff6f
        - sha256:b9fed67cf0f951573f06d560ac2f10f2a4bbdc6697770113a2fc396ea2cb2565
        - sha256:b76b4eed9b27fd63ddb0877efdd2d15835fdcb6baa745cb85b66e5d016ac2878
        - sha256:851b3735ca7e8102e661872390e3bce88f8901bece95c25a0c8bb9ecb8a23d32
        - sha256:bf4133139d77fc706d8f572e6b7d82871d82ec7ef25d685c2351bdacfb701415
        - sha256:306becd6e22ab6e8e4f36b6bdafd9c92e867c98a5ce517b27fdd27760ee7ae41
        - sha256:3c61742cb0bc6c1ac117a7e5f422c129832f0c295af49e01d8a6066df8cfc04d
        - sha256:b435857b9efcbfc12f8c326ef0383f26416272260455bbca2cd8d8eca470c546
        - sha256:7d972cfa8439bdcb35a7be78b7ef86d73b34b808c74be56dfa785c8a93b851bf
        - sha256:72068b206ee809c9c596b57b5287259ea41ddb4774d86725b19f35bf56aa32a9
    pywin32-ctypes:
      version: "0.2.3"
      hashes:
        - sha256:8a1513379d709975552d202d942d9837758905c8d01eb82b8bcc30918929e7b8
        - sha256:d162dc04946d704503b2edc4d55f3dba5c1d539ead017afa00142c38b9885755
    pynavlib:
      version: "0.9.4"
      hashes:
        - sha256:fdd5ab5b6e0a2c9bbcebb154ac7303daf845865a1649be04e1bd8e8e5889401f
        - sha256:493c4b3cacc939b021a694d99723106dbd7ee5515ad4dfc1c7fc8219ef20cf3a
        - sha256:332831553a70be05fe58c43a08109b42970cfedc6086ffb4306859142a0e9210
        - sha256:9173f61ad83172c306b92bbe38f949889c158cd6dfdc924db01f257a437bf2a6

  Macos:
    pynavlib:
      version: "0.9.4"
      hashes:
        - sha256:567efd0af97f9014326898b209eea94d9f5cc58e9f589ccf8354584568fcb87d
        - sha256:f0d7ce426e816788aa96b419fd7da263eafb99aca46ce3b6e5dbaf2bbf6b614a
        - sha256:33962a322033a78db05a8c2cc3d59e057fbea5b04879c3c54e2fe3041d691a12
        - sha256:d06d94b1dee4ba024b4a121869e572f571673a3b8c15b4055f52236d43c19a02

pip_requirements_dev:
  any_os:
    pytest: {}
    pyyaml: {}
    sip: { version: "6.5.1" }
    jinja2: {}

pip_requirements_installer:
  any_os:
    pyinstaller: { version: "6.11.1" }
    pyinstaller-hooks-contrib: {}

python_translation_source_folders:
  - cura
  - plugins
qml_translation_source_folders:
  - resources/qml
  - plugins

extra_dependencies:
  conan:
    version: "2.7.1"
    sources_url: https://github.com/conan-io/conan
    license: MIT
    summary: Conan C/C++ package manager